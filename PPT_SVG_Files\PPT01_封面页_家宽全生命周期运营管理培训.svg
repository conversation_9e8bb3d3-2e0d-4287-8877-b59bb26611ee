<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: normal; fill: #3B82F6; text-anchor: middle; }
      .info-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle; }
      .logo-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 8; opacity: 0.3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 0 200 Q 480 100 960 200 T 1920 200" class="accent-curve"/>
  <path d="M 0 880 Q 480 980 960 880 T 1920 880" class="accent-curve"/>
  
  <!-- 四川移动Logo区域 -->
  <rect x="100" y="80" width="200" height="80" fill="#1E3A8A" rx="10"/>
  <text x="200" y="135" class="logo-text" fill="white" text-anchor="middle">四川移动</text>
  
  <!-- 爱家品牌Logo区域 -->
  <rect x="1520" y="80" width="200" height="80" fill="#3B82F6" rx="10"/>
  <text x="1620" y="135" class="logo-text" fill="white" text-anchor="middle">爱家</text>
  
  <!-- 主标题 -->
  <text x="960" y="400" class="title-text">高质量家宽全流程运营</text>
  <text x="960" y="480" class="title-text">从入网到挽留的实战方法与工具赋能</text>
  
  <!-- 副标题 -->
  <text x="960" y="580" class="subtitle-text">四川移动家宽一线营销服务人员技能提升专项培训</text>
  
  <!-- 装饰元素 -->
  <circle cx="300" cy="650" r="80" fill="#E0F2FE" opacity="0.6"/>
  <circle cx="1620" cy="650" r="80" fill="#DBEAFE" opacity="0.6"/>
  
  <!-- 日期和讲师信息 -->
  <text x="960" y="800" class="info-text">培训日期：2025年8月</text>
  <text x="960" y="850" class="info-text">讲师：[讲师姓名]</text>
  
  <!-- 底部装饰线 -->
  <rect x="760" y="920" width="400" height="4" fill="#3B82F6" rx="2"/>
</svg>
