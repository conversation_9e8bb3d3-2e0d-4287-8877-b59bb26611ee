<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .signal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">网速不够用？抓住提速升级机会点</text>
  <text x="960" y="140" class="subtitle-text">"促升档"1：宽带提速正当时</text>
  
  <!-- 识别信号 -->
  <rect x="100" y="180" width="1720" height="300" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#DC2626">识别信号：</text>
  
  <!-- 信号列表 -->
  <rect x="200" y="270" width="700" height="80" fill="#FEE2E2" rx="10"/>
  <text x="220" y="300" class="signal-text">• 用户抱怨卡顿、转圈圈</text>
  <text x="220" y="330" class="signal-text">• 现场看到用户家设备多</text>
  
  <rect x="950" y="270" width="700" height="80" fill="#FEE2E2" rx="10"/>
  <text x="970" y="300" class="signal-text">• 经常多人同时上网</text>
  <text x="970" y="330" class="signal-text">• 用户主动咨询提速</text>
  
  <rect x="200" y="370" width="1450" height="80" fill="#FEE2E2" rx="10"/>
  <text x="220" y="400" class="signal-text">• 用户流量使用量长期接近套餐上限</text>
  <text x="220" y="430" class="signal-text">• 家中有4K电视、游戏设备等高带宽需求设备</text>
  
  <!-- 话术黄金三问 -->
  <rect x="100" y="510" width="1720" height="450" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="560" class="section-title" fill="#059669">话术黄金三问 (复习)：</text>
  
  <!-- 第一问：挖痛点 -->
  <rect x="200" y="600" width="1520" height="100" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="550" y="630" class="step-text" fill="#059669">1️⃣ 挖痛点</text>
  <text x="220" y="660" class="speech-text">"感觉网速怎么样？看高清视频/玩游戏会不会卡？"</text>
  <text x="220" y="685" class="speech-text">"家里几个人同时用网？会不会互相影响？"</text>
  
  <!-- 第二问：推方案 -->
  <rect x="200" y="720" width="1520" height="100" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="550" y="750" class="step-text" fill="#059669">2️⃣ 推方案</text>
  <text x="220" y="780" class="speech-text">"现在升级到XX兆套餐，速度快很多，还送[权益]，性价比很高。"</text>
  <text x="220" y="805" class="speech-text">"像您家这种情况，千兆宽带最合适，全家人用网都不卡。"</text>
  
  <!-- 第三问：促成交 -->
  <rect x="200" y="840" width="1520" height="100" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="550" y="870" class="step-text" fill="#059669">3️⃣ 促成交</text>
  <text x="220" y="900" class="speech-text">"这个月办理还有[限时优惠]，要不要现在帮您升级？"</text>
  <text x="220" y="925" class="speech-text">"升级后立即生效，今天就能体验到更快的网速。"</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="980" width="1520" height="80" fill="#FFFBEB" rx="20"/>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">关键技巧</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">发现需求 → 匹配方案 → 促成升级</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="330" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="330" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="50" cy="720" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="720" r="25" fill="#059669" opacity="0.3"/>
</svg>
