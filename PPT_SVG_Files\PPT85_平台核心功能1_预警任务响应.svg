<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .importance-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">任务来了？平台第一时间告诉你！</text>
  <text x="960" y="140" class="subtitle-text">平台核心功能1：预警任务，及时响应</text>
  
  <!-- 功能界面提示 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="section-title" fill="#3B82F6">功能界面：</text>
  <text x="150" y="260" class="step-text">[截图：爱家APP任务中心/预警列表]</text>
  
  <!-- 怎么用 -->
  <rect x="100" y="310" width="1720" height="550" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="150" y="360" class="section-title" fill="#1E3A8A">怎么用？</text>
  
  <!-- 每日查看 -->
  <rect x="200" y="400" width="1520" height="80" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="430" class="step-title" fill="#3B82F6">每日查看：</text>
  <text x="380" y="450" class="step-text">养成上班先看任务列表的习惯</text>
  
  <!-- 看懂任务 -->
  <rect x="200" y="500" width="1520" height="100" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="530" class="step-title" fill="#059669">看懂任务：</text>
  <text x="380" y="550" class="step-text">理解任务类型（如"高危用户挽留"、"离线用户核查"、"投诉工单处理"）</text>
  <text x="220" y="580" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">不同任务类型有不同的处理要求和时限</text>
  
  <!-- 及时处理 -->
  <rect x="200" y="620" width="1520" height="80" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="650" class="step-title" fill="#EA580C">及时处理：</text>
  <text x="380" y="670" class="step-text">按照任务要求和时限，联系用户、上门服务或上报情况</text>
  
  <!-- 闭环反馈 -->
  <rect x="200" y="720" width="1520" height="80" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="220" y="750" class="step-title" fill="#7C3AED">闭环反馈：</text>
  <text x="380" y="770" class="step-text">处理完毕后，在系统中及时反馈处理结果</text>
  
  <!-- 重要性 -->
  <rect x="200" y="820" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="860" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">重要性：</text>
  <text x="960" y="890" class="importance-text" fill="#DC2626">及时处理预警任务，是"控流失"、"控风险"的关键！</text>
  <text x="960" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">每一个预警都可能关系到用户的去留</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="980" width="1520" height="80" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">响应及时，处理到位，反馈准确</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让平台成为我们工作的得力助手</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
