<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .principle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">"迟到"了怎么办？—— 真诚沟通是关键</text>
  <text x="960" y="140" class="subtitle-text">话术库：应对安装延迟</text>
  
  <!-- 四步法回顾 -->
  <rect x="100" y="180" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">回顾"四步法"话术</text>
  
  <!-- 道歉安抚 -->
  <rect x="200" y="280" width="700" height="120" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="3"/>
  <text x="220" y="320" class="step-title" fill="#DC2626">1️⃣ 道歉安抚：</text>
  <text x="220" y="360" class="speech-text">"非常抱歉让您久等了，这确实是我们的</text>
  <text x="220" y="385" class="speech-text">责任，给您带来不便了。"</text>
  
  <!-- 解释原因 -->
  <rect x="950" y="280" width="700" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="3"/>
  <text x="970" y="320" class="step-title" fill="#EA580C">2️⃣ 解释原因：</text>
  <text x="970" y="360" class="speech-text">"由于[前一个用户的问题比较复杂/路上</text>
  <text x="970" y="385" class="speech-text">堵车/设备临时故障]，耽误了时间。"</text>
  
  <!-- 解决方案 -->
  <rect x="200" y="420" width="700" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="3"/>
  <text x="220" y="460" class="step-title" fill="#059669">3️⃣ 解决方案：</text>
  <text x="220" y="500" class="speech-text">"工程师预计[具体时间]到达，我们申请了</text>
  <text x="220" y="525" class="speech-text">[话费减免/服务补偿]作为歉意。"</text>
  
  <!-- 确认跟进 -->
  <rect x="950" y="420" width="700" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="3"/>
  <text x="970" y="460" class="step-title" fill="#7C3AED">4️⃣ 确认跟进：</text>
  <text x="970" y="500" class="speech-text">"您这个时间方便吗？我会持续跟进，</text>
  <text x="970" y="525" class="speech-text">确保师傅按时到达。"</text>
  
  <!-- 完整话术示例 -->
  <rect x="200" y="570" width="1520" height="150" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💬 完整话术示例</text>
  <text x="220" y="650" class="speech-text">"张先生，非常抱歉让您久等了。由于前一个用户家里线路比较复杂，</text>
  <text x="220" y="680" class="speech-text">师傅处理时间长了一些。现在师傅预计下午3点能到您家，我们申请了</text>
  <text x="220" y="710" class="speech-text">20元话费作为补偿。您看这个时间方便吗？"</text>
  
  <!-- 关键原则 -->
  <rect x="200" y="800" width="1520" height="150" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="960" y="840" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">🔑 关键原则</text>
  <text x="960" y="880" class="principle-text" fill="#DC2626">不推诿，给预期，有补偿</text>
  <text x="960" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">承担责任 + 明确时间 + 实际补偿 = 用户理解</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="340" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="340" r="30" fill="#EA580C" opacity="0.3"/>
  <circle cx="50" cy="480" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="480" r="25" fill="#7C3AED" opacity="0.3"/>
  
  <!-- 时钟图标装饰 -->
  <text x="50" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #F59E0B;">⏰</text>
  <text x="1870" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #F59E0B;">⏰</text>
</svg>
