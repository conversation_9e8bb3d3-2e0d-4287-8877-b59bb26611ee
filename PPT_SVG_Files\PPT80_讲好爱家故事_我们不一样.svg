<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .value-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .correspond-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; }
      .goal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">移动爱家：不止快，更是"智""享""护""省"</text>
  <text x="960" y="140" class="subtitle-text">讲好"爱家"故事：我们不一样！</text>
  
  <!-- 核心价值点 -->
  <rect x="100" y="180" width="1720" height="650" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">核心价值点 (一线版解读)：</text>
  
  <!-- 智连 -->
  <rect x="200" y="280" width="700" height="150" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="550" y="320" class="value-title" fill="#3B82F6">[智连]</text>
  <text x="220" y="360" class="value-text">"网速快，信号好，全屋都能跑满速！"</text>
  <text x="220" y="390" class="correspond-text">(对应：千兆宽带、WiFi 6、Mesh组网)</text>
  <text x="220" y="415" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #3B82F6;">💡 让用户感受到连接的智能和稳定</text>
  
  <!-- 智享 -->
  <rect x="950" y="280" width="700" height="150" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="1300" y="320" class="value-title" fill="#059669">[智享]</text>
  <text x="970" y="360" class="value-text">"看电视、玩游戏爽歪歪！手机照片自动存云端，方便！"</text>
  <text x="970" y="390" class="correspond-text">(对应：高清IPTV、低延迟网络、家庭云)</text>
  <text x="970" y="415" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #059669;">🎯 让用户享受到智能生活的便利</text>
  
  <!-- 智护 -->
  <rect x="200" y="450" width="700" height="150" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="550" y="490" class="value-title" fill="#EA580C">[智护]</text>
  <text x="220" y="530" class="value-text">"家里安全随时看，上网还有安全防护，放心！"</text>
  <text x="220" y="560" class="correspond-text">(对应：智能摄像头、云存储、绿色上网)</text>
  <text x="220" y="585" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #EA580C;">🛡️ 让用户感受到安全的守护</text>
  
  <!-- 智省 -->
  <rect x="950" y="450" width="700" height="150" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="1300" y="490" class="value-title" fill="#7C3AED">[智省]</text>
  <text x="970" y="530" class="value-text">"办个融合套餐，全家话费宽带一起搞定，划算！"</text>
  <text x="970" y="560" class="correspond-text">(对应：融合套餐优惠、便捷服务)</text>
  <text x="970" y="585" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #7C3AED;">💰 让用户体验到实实在在的省钱省心</text>
  
  <!-- 传递要点 -->
  <rect x="200" y="620" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">传递要点</text>
  <text x="220" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A;">语言要简单直接，让用户一听就懂，一懂就记住</text>
  <text x="220" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #6B7280;">避免技术术语，多用生活化的表达方式</text>
  
  <!-- 目标 -->
  <rect x="200" y="860" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="910" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">目标：</text>
  <text x="960" y="950" class="goal-text" fill="#1E3A8A">用简单直接的语言，让用户记住爱家品牌的好处</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让每一次服务都成为品牌传播的机会</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="550" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="550" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
