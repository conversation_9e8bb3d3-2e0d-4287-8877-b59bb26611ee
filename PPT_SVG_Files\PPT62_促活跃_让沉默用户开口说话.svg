<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .method-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .target-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">互动激活：拉近与用户的距离</text>
  <text x="960" y="140" class="subtitle-text">"促活跃"：让沉默用户"开口说话"</text>
  
  <!-- 对象说明 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6;">对象：</text>
  <text x="150" y="260" class="target-text">感觉用户不怎么用网？新装用户还没熟悉业务？</text>
  
  <!-- 方法与话术 -->
  <rect x="100" y="310" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="960" y="360" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">方法与话术</text>
  
  <!-- 方法1：福利提醒 -->
  <rect x="200" y="400" width="1520" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="440" class="method-title" fill="#059669">【方法1：福利提醒】</text>
  <text x="220" y="470" class="speech-text">"X先生，提醒您一下，您套餐里赠送的那个[视频会员/云盘空间]</text>
  <text x="220" y="500" class="speech-text">还没用呢，挺划算的，别浪费了。您可以通过爱家APP…"</text>
  
  <!-- 方法2：活动引导 -->
  <rect x="200" y="540" width="1520" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="580" class="method-title" fill="#3B82F6">【方法2：活动引导】</text>
  <text x="220" y="610" class="speech-text">"我们最近有个[签到领流量/宽带知识问答]活动，挺有意思的，</text>
  <text x="220" y="640" class="speech-text">您可以在爱家APP上看看，参与还有小礼品。"</text>
  
  <!-- 方法3：主动关怀 -->
  <rect x="200" y="680" width="1520" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="720" class="method-title" fill="#EA580C">【方法3：主动关怀(结合预警)】</text>
  <text x="220" y="750" class="speech-text">"王阿姨，系统提示您家宽带好几天没用了，是遇到什么问题了吗？</text>
  <text x="220" y="780" class="speech-text">网络还正常吧？"</text>
  
  <!-- 关键要点 -->
  <rect x="200" y="830" width="1520" height="60" fill="#FFFBEB" rx="15"/>
  <text x="220" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B;">[关键]：</text>
  <text x="350" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">主动关心，提供价值，引导互动</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="940" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">核心理念</text>
  <text x="960" y="1000" class="key-text" fill="#1E3A8A">用心服务，主动关怀，让用户感受到移动的温度</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">沉默的用户往往蕴藏着巨大的价值潜力</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="600" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="600" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
