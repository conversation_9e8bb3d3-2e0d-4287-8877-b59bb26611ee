<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
      .step-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; text-anchor: middle; fill: #4B5563; }
      .step-number { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: white; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">完美收官：规范验收赢好感</text>
  <text x="960" y="140" class="subtitle-text">用户验收流程：标准五步法</text>
  
  <!-- 流程图主框架 -->
  <rect x="100" y="200" width="1720" height="700" fill="#F8FAFC" rx="30" stroke="#3B82F6" stroke-width="4"/>
  
  <!-- 步骤1：服务复核 -->
  <rect x="200" y="280" width="280" height="200" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <circle cx="340" cy="330" r="30" fill="#3B82F6"/>
  <text x="340" y="340" class="step-number">1</text>
  <text x="340" y="380" class="step-title" fill="#3B82F6">服务复核</text>
  <text x="340" y="410" class="step-desc">确认安装完成</text>
  <text x="340" y="435" class="step-desc">现场整洁</text>
  <text x="340" y="460" class="step-desc">设备正常运行</text>
  
  <!-- 箭头1 -->
  <path d="M 480 380 L 540 380" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 步骤2：性能演示 -->
  <rect x="570" y="280" width="280" height="200" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <circle cx="710" cy="330" r="30" fill="#059669"/>
  <text x="710" y="340" class="step-number">2</text>
  <text x="710" y="380" class="step-title" fill="#059669">性能演示</text>
  <text x="710" y="410" class="step-desc">当面测速</text>
  <text x="710" y="435" class="step-desc">展示结果</text>
  <text x="710" y="460" class="step-desc">确认达标</text>
  
  <!-- 箭头2 -->
  <path d="M 850 380 L 910 380" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 步骤3：功能介绍 -->
  <rect x="940" y="280" width="280" height="200" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <circle cx="1080" cy="330" r="30" fill="#EA580C"/>
  <text x="1080" y="340" class="step-number">3</text>
  <text x="1080" y="380" class="step-title" fill="#EA580C">功能介绍</text>
  <text x="1080" y="410" class="step-desc">WiFi密码</text>
  <text x="1080" y="435" class="step-desc">APP用法</text>
  <text x="1080" y="460" class="step-desc">基本操作</text>
  
  <!-- 箭头3 -->
  <path d="M 1220 380 L 1280 380" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 步骤4：电子签收 -->
  <rect x="1310" y="280" width="280" height="200" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <circle cx="1450" cy="330" r="30" fill="#7C3AED"/>
  <text x="1450" y="340" class="step-number">4</text>
  <text x="1450" y="380" class="step-title" fill="#7C3AED">电子签收</text>
  <text x="1450" y="410" class="step-desc">引导用户在</text>
  <text x="1450" y="435" class="step-desc">手机/Pad</text>
  <text x="1450" y="460" class="step-desc">完成签名</text>
  
  <!-- 向下箭头 -->
  <path d="M 1450 480 L 1450 540" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowdown)"/>
  
  <!-- 步骤5：温馨提示 -->
  <rect x="1310" y="570" width="280" height="200" fill="#FDF2F8" rx="20" stroke="#EC4899" stroke-width="3"/>
  <circle cx="1450" cy="620" r="30" fill="#EC4899"/>
  <text x="1450" y="630" class="step-number">5</text>
  <text x="1450" y="670" class="step-title" fill="#EC4899">温馨提示</text>
  <text x="1450" y="700" class="step-desc">使用建议</text>
  <text x="1450" y="725" class="step-desc">联系方式</text>
  <text x="1450" y="750" class="step-desc">礼貌道别</text>
  
  <!-- 向左箭头回到起点 -->
  <path d="M 1310 670 L 200 670 L 200 480" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="10,5" opacity="0.6"/>
  
  <!-- 中央强调区域 -->
  <rect x="400" y="550" width="720" height="180" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  <text x="760" y="600" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">🎯 核心要点</text>
  <text x="760" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">每一步都不能省略</text>
  <text x="760" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">规范操作保护自己，也让用户放心</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="820" width="1520" height="100" fill="#EFF6FF" rx="20"/>
  <text x="960" y="860" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">标准化验收 = 专业形象 + 用户满意</text>
  <text x="960" y="900" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">五步走完，工作才算真正完成！</text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6"/>
    </marker>
    <marker id="arrowdown" markerWidth="10" markerHeight="7" refX="5" refY="6" orient="auto">
      <polygon points="0 0, 10 0, 5 7" fill="#3B82F6"/>
    </marker>
  </defs>
</svg>
