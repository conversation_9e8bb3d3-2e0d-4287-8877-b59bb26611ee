<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; font-style: italic; }
      .point-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">模拟真实场景，提升应对能力</text>
  <text x="960" y="140" class="subtitle-text">角色扮演：演练场景模拟</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">🎬</text>
  
  <!-- 演练场景 -->
  <rect x="100" y="300" width="1720" height="400" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">常见演练场景</text>
  
  <!-- 场景1：网速慢投诉 -->
  <rect x="200" y="390" width="700" height="120" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="550" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">场景1：网速慢投诉</text>
  <text x="220" y="460" class="scenario-text">"师傅，你们装的网太慢了！说好300M，</text>
  <text x="220" y="485" class="scenario-text">现在卧室看视频都卡，这怎么用啊？"</text>
  
  <!-- 场景2：服务态度投诉 -->
  <rect x="950" y="390" width="700" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="1300" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C; text-anchor: middle;">场景2：服务态度投诉</text>
  <text x="970" y="460" class="scenario-text">"上次那个师傅态度太差了，进门鞋都不换，</text>
  <text x="970" y="485" class="scenario-text">东西乱放，一点都不专业！"</text>
  
  <!-- 场景3：安装延迟抱怨 -->
  <rect x="200" y="530" width="700" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="550" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">场景3：安装延迟抱怨</text>
  <text x="220" y="600" class="scenario-text">"说好上午来装，现在都下午3点了！</text>
  <text x="220" y="625" class="scenario-text">我请假在家等，你们太不守时了！"</text>
  
  <!-- 场景4：技术问题咨询 -->
  <rect x="950" y="530" width="700" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="1300" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">场景4：技术问题咨询</text>
  <text x="970" y="600" class="scenario-text">"师傅，我家WiFi总是断，是不是路由器</text>
  <text x="970" y="625" class="scenario-text">有问题？能不能帮我看看？"</text>
  
  <!-- 演练要求 -->
  <rect x="100" y="730" width="1720" height="200" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  <text x="150" y="780" class="section-title" fill="#F59E0B">演练要求：</text>
  <text x="200" y="820" class="point-text">• 每组选择1-2个场景进行演练</text>
  <text x="200" y="850" class="point-text">• 扮演用户的同学要情绪真实，提出合理质疑</text>
  <text x="200" y="880" class="point-text">• 扮演师傅的同学要运用所学话术和技巧</text>
  <text x="200" y="910" class="point-text">• 观察员要记录关键表现，准备点评反馈</text>
  
  <!-- 点评标准 -->
  <rect x="200" y="960" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">点评标准</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">共情能力 + 话术运用 + 问题解决 + 专业态度</text>
  <text x="960" y="1050" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #6B7280; text-anchor: middle;">讲师和其他学员共同点评，互相学习提升</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="450" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="450" r="30" fill="#EA580C" opacity="0.3"/>
  <circle cx="50" cy="590" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="590" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
