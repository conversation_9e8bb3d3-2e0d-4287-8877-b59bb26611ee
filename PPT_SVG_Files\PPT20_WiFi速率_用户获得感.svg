<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .standard-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">速率达标：用户最直观的"获得感"</text>
  <text x="960" y="140" class="subtitle-text">施工核心指标2：WiFi速率 (90%是承诺)</text>
  
  <!-- 怎么测 -->
  <rect x="100" y="180" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">怎么测？</text>
  <text x="150" y="280" class="content-text">必须在用户主要活动区域（如客厅电视旁、主卧床头）</text>
  <text x="150" y="320" class="content-text">连接光猫WiFi，使用爱家APP的"一键测速"功能。</text>
  <text x="150" y="360" class="content-text">📱 多点测试，确保覆盖区域都达标</text>
  
  <!-- 标准 -->
  <rect x="100" y="410" width="1720" height="180" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="460" class="section-title" fill="#059669">标准：</text>
  <text x="150" y="510" class="standard-text" fill="#059669">下载速率 ≥ 用户办理套餐带宽的90%</text>
  <text x="150" y="560" class="example-text">例如：300M套餐，至少要测到270M以上</text>
  
  <!-- 套餐对比示例 -->
  <rect x="150" y="620" width="350" height="140" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="325" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">100M套餐</text>
  <text x="325" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; text-anchor: middle;">达标：≥90M</text>
  <text x="325" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">✓ 95M</text>
  
  <rect x="550" y="620" width="350" height="140" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="725" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">300M套餐</text>
  <text x="725" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; text-anchor: middle;">达标：≥270M</text>
  <text x="725" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">✓ 285M</text>
  
  <rect x="950" y="620" width="350" height="140" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1125" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">500M套餐</text>
  <text x="1125" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; text-anchor: middle;">达标：≥450M</text>
  <text x="1125" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">✓ 478M</text>
  
  <rect x="1350" y="620" width="350" height="140" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1525" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #DC2626; text-anchor: middle;">1000M套餐</text>
  <text x="1525" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; text-anchor: middle;">达标：≥900M</text>
  <text x="1525" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">✗ 850M</text>
  
  <!-- 意义 -->
  <rect x="100" y="790" width="850" height="150" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="130" y="840" class="section-title" fill="#F59E0B">意义：</text>
  <text x="130" y="880" class="highlight-text" fill="#DC2626">这是我们对用户的基本承诺，</text>
  <text x="130" y="920" class="highlight-text" fill="#DC2626">必须兑现！</text>
  
  <!-- 常见原因 -->
  <rect x="970" y="790" width="750" height="150" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="1000" y="840" class="section-title" fill="#EA580C">不达标常见原因：</text>
  <text x="1000" y="870" class="example-text">• 光猫/路由性能限制</text>
  <text x="1000" y="900" class="example-text">• 摆放位置不佳</text>
  <text x="1000" y="930" class="example-text">• 信号干扰、后台线路问题</text>
  
  <!-- 底部提醒 -->
  <rect x="200" y="970" width="1520" height="80" fill="#EFF6FF" rx="15"/>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">速率达标是用户满意的第一要素！</text>
</svg>
