<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .tip-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">简单几招，改善家中WiFi</text>
  <text x="960" y="140" class="subtitle-text">WiFi优化基础：从源头改善信号</text>
  
  <!-- 优化方法 -->
  <rect x="100" y="180" width="1720" height="700" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">基础优化方法</text>
  
  <!-- 选对位置 -->
  <rect x="200" y="280" width="700" height="180" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="550" y="320" class="icon-text">📍</text>
  <text x="550" y="370" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">选对位置</text>
  <text x="220" y="410" class="tip-text">• 路由器放房屋中心、开阔处、高一点的地方</text>
  <text x="220" y="440" class="tip-text">• 避免放在角落、柜子里、地面上</text>
  
  <!-- 远离干扰 -->
  <rect x="950" y="280" width="700" height="180" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="1300" y="320" class="icon-text">🚫</text>
  <text x="1300" y="370" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">远离干扰</text>
  <text x="970" y="410" class="tip-text">• 远离微波炉、无绳电话、大功率电器</text>
  <text x="970" y="440" class="tip-text">• 避开金属障碍物、厚墙体</text>
  
  <!-- 天线摆放 -->
  <rect x="200" y="480" width="700" height="180" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="550" y="520" class="icon-text">📡</text>
  <text x="550" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #EA580C; text-anchor: middle;">天线摆放</text>
  <text x="220" y="610" class="tip-text">• 外置天线可尝试不同角度</text>
  <text x="220" y="640" class="tip-text">• 垂直+水平组合效果更好</text>
  
  <!-- 定期重启 -->
  <rect x="950" y="480" width="700" height="180" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="1300" y="520" class="icon-text">🔄</text>
  <text x="1300" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">定期重启</text>
  <text x="970" y="610" class="tip-text">• 重启路由器和光猫，清除缓存</text>
  <text x="970" y="640" class="tip-text">• 有时能解决小问题，提升性能</text>
  
  <!-- 实用建议 -->
  <rect x="200" y="690" width="1520" height="150" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="730" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 实用建议</text>
  <text x="220" y="770" class="content-text">这些都是免费的基础优化方法，用户可以自己尝试</text>
  <text x="220" y="800" class="content-text">如果基础优化效果不明显，再考虑设备升级或增值服务</text>
  <text x="220" y="830" class="highlight-text" fill="#DC2626">先免费后付费，用户更容易接受！</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="910" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="940" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">🎯 目标</text>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">用专业知识帮助用户，体现服务价值</text>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">简单优化也能带来明显改善</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="580" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="580" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
