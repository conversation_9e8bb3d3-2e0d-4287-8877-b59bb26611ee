<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .stakeholder-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; text-anchor: middle; }
      .benefit-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #059669; text-anchor: middle; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">做好服务，成就你我</text>
  
  <!-- 对用户 -->
  <rect x="100" y="150" width="500" height="300" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="4"/>
  <text x="350" y="200" class="icon-text">👥</text>
  <text x="350" y="260" class="stakeholder-title" fill="#059669">对用户</text>
  <text x="150" y="310" class="benefit-text">• 提供稳定、高速、舒心的</text>
  <text x="170" y="350" class="benefit-text">用网体验</text>
  <text x="150" y="390" class="benefit-text">• 享受专业贴心的服务</text>
  <text x="150" y="430" class="benefit-text">• 物超所值的产品体验</text>
  
  <!-- 对公司 -->
  <rect x="710" y="150" width="500" height="300" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="200" class="icon-text">🏢</text>
  <text x="960" y="260" class="stakeholder-title" fill="#3B82F6">对公司</text>
  <text x="760" y="310" class="benefit-text">• 赢得口碑，提升品牌形象</text>
  <text x="760" y="350" class="benefit-text">• 减少流失，增加收入</text>
  <text x="760" y="390" class="benefit-text">• 降低投诉，节约成本</text>
  <text x="760" y="430" class="benefit-text">• 可持续发展竞争优势</text>
  
  <!-- 对自己 -->
  <rect x="1320" y="150" width="500" height="300" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="4"/>
  <text x="1570" y="200" class="icon-text">🎯</text>
  <text x="1570" y="260" class="stakeholder-title" fill="#EA580C">对自己</text>
  <text x="1370" y="310" class="benefit-text">• 提升技能，增强专业能力</text>
  <text x="1370" y="350" class="benefit-text">• 获得认可，职业发展</text>
  <text x="1370" y="390" class="benefit-text">• 实现个人价值</text>
  <text x="1370" y="430" class="benefit-text">• 工作成就感提升</text>
  
  <!-- 连接线 -->
  <path d="M 600 300 L 710 300" fill="none" stroke="#6B7280" stroke-width="6" stroke-dasharray="10,5"/>
  <path d="M 1210 300 L 1320 300" fill="none" stroke="#6B7280" stroke-width="6" stroke-dasharray="10,5"/>
  
  <!-- 核心理念区域 -->
  <rect x="200" y="500" width="1520" height="200" fill="#F8FAFC" rx="30" stroke="#1E3A8A" stroke-width="4"/>
  <text x="960" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 56px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">核心理念</text>
  <text x="960" y="640" class="core-text">专业服务，创造多赢！</text>
  
  <!-- 质量循环图 -->
  <rect x="300" y="750" width="1320" height="250" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  
  <!-- 循环箭头和文字 -->
  <circle cx="500" cy="850" r="60" fill="#059669"/>
  <text x="500" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">优质服务</text>
  
  <path d="M 560 850 Q 650 800 740 850" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <circle cx="800" cy="850" r="60" fill="#3B82F6"/>
  <text x="800" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">用户满意</text>
  
  <path d="M 860 850 Q 950 800 1040 850" fill="none" stroke="#7C3AED" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <circle cx="1100" cy="850" r="60" fill="#7C3AED"/>
  <text x="1100" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">口碑传播</text>
  
  <path d="M 1160 850 Q 1250 800 1340 850" fill="none" stroke="#EA580C" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <circle cx="1400" cy="850" r="60" fill="#EA580C"/>
  <text x="1400" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">业务增长</text>
  
  <path d="M 1340 890 Q 1250 940 1160 890 Q 950 940 860 890 Q 650 940 560 890" fill="none" stroke="#059669" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">良性循环，持续发展</text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6"/>
    </marker>
  </defs>
</svg>
