<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .indicator-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; font-weight: bold; }
      .indicator-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .trend-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
      .question-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #DC2626; text-anchor: middle; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">几个数字看懂我们的服务表现</text>
  
  <!-- 指标1：装机一次成功率 -->
  <rect x="80" y="150" width="850" height="200" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <circle cx="180" cy="220" r="50" fill="#3B82F6"/>
  <text x="180" y="235" class="icon-text" fill="white">🔧</text>
  <text x="280" y="190" class="indicator-title" fill="#3B82F6">装机一次成功率</text>
  <text x="280" y="230" class="indicator-desc">反映我们上门安装的效率和专业度</text>
  <text x="280" y="270" class="indicator-desc">目标：一次上门就搞定，不让用户等第二次</text>
  <rect x="700" y="300" width="180" height="40" fill="#059669" rx="10"/>
  <text x="790" y="325" class="trend-text" fill="white">越高越好！</text>
  
  <!-- 指标2：测速达标率 -->
  <rect x="990" y="150" width="850" height="200" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <circle cx="1090" cy="220" r="50" fill="#059669"/>
  <text x="1090" y="235" class="icon-text" fill="white">📶</text>
  <text x="1190" y="190" class="indicator-title" fill="#059669">测速达标率</text>
  <text x="1190" y="230" class="indicator-desc">用户网速达不达标？这是最基本的承诺！</text>
  <text x="1190" y="270" class="indicator-desc">目标：≥90%套餐带宽，说到做到</text>
  <rect x="1610" y="300" width="180" height="40" fill="#059669" rx="10"/>
  <text x="1700" y="325" class="trend-text" fill="white">越高越好！</text>
  
  <!-- 指标3：万投比 -->
  <rect x="80" y="400" width="850" height="200" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <circle cx="180" cy="470" r="50" fill="#EA580C"/>
  <text x="180" y="485" class="icon-text" fill="white">📞</text>
  <text x="280" y="440" class="indicator-title" fill="#EA580C">万投比</text>
  <text x="280" y="480" class="indicator-desc">一万个用户里有多少人投诉？</text>
  <text x="280" y="520" class="indicator-desc">反映服务质量和用户满意度</text>
  <rect x="700" y="550" width="180" height="40" fill="#DC2626" rx="10"/>
  <text x="790" y="575" class="trend-text" fill="white">越低越好！</text>
  
  <!-- 指标4：NPS净推荐值 -->
  <rect x="990" y="400" width="850" height="200" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <circle cx="1090" cy="470" r="50" fill="#7C3AED"/>
  <text x="1090" y="485" class="icon-text" fill="white">👥</text>
  <text x="1190" y="440" class="indicator-title" fill="#7C3AED">NPS净推荐值</text>
  <text x="1190" y="480" class="indicator-desc">用户满意度的综合体现</text>
  <text x="1190" y="520" class="indicator-desc">我们追求的终极目标！</text>
  <rect x="1610" y="550" width="180" height="40" fill="#059669" rx="10"/>
  <text x="1700" y="575" class="trend-text" fill="white">越高越好！</text>
  
  <!-- 联系自身区域 -->
  <rect x="200" y="650" width="1520" height="200" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="710" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">联系自身</text>
  <text x="960" y="770" class="question-text">想想我的工作，如何能帮助提升这些指标？</text>
  <text x="960" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">每一次规范操作、每一次用心服务，都在为这些数字加分！</text>
  
  <!-- 底部强调 -->
  <rect x="300" y="900" width="1320" height="80" fill="#EFF6FF" rx="15"/>
  <text x="960" y="950" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">数字背后是用户的信任与口碑</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="300" r="25" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="300" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="550" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="550" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
