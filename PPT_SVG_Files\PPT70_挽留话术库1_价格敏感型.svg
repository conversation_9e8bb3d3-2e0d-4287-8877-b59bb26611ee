<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .point-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">用户觉得贵？算笔"明白账"</text>
  <text x="960" y="140" class="subtitle-text">挽留话术库1：应对"价格敏感型"</text>
  
  <!-- 沟通要点 -->
  <rect x="100" y="180" width="1720" height="750" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">沟通要点：</text>
  
  <!-- 理解用户 -->
  <rect x="200" y="270" width="1520" height="100" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="310" class="point-title" fill="#3B82F6">理解用户对价格的关注</text>
  <text x="220" y="340" class="content-text">先表示理解，不要直接反驳用户的价格顾虑</text>
  
  <!-- 对比价值 -->
  <rect x="200" y="390" width="1520" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="430" class="point-title" fill="#059669">对比价值：</text>
  <text x="220" y="460" class="speech-text">"虽然月费看起来XX元，但我们包含了[权益1、权益2]，</text>
  <text x="220" y="490" class="speech-text">算下来性价比很高…"</text>
  
  <!-- 推荐替代方案 -->
  <rect x="200" y="530" width="1520" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="570" class="point-title" fill="#EA580C">推荐替代方案（若有）：</text>
  <text x="220" y="600" class="speech-text">"如果您觉得这个套餐暂时用不了这么多，我们还有一款XX元的套餐，</text>
  <text x="220" y="630" class="speech-text">也挺划算的…"</text>
  
  <!-- 强调优势 -->
  <rect x="200" y="670" width="1520" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="220" y="710" class="point-title" fill="#7C3AED">强调网络/服务优势：</text>
  <text x="220" y="740" class="speech-text">"一分钱一分货，我们移动的网络稳定性和服务还是有保障的…"</text>
  <text x="220" y="770" class="content-text">突出移动品牌的可靠性和服务质量</text>
  
  <!-- 提供优惠 -->
  <rect x="200" y="810" width="1520" height="100" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <text x="220" y="850" class="point-title" fill="#F59E0B">提供小额优惠/赠送：</text>
  <text x="220" y="880" class="speech-text">"看您是老用户了，我帮您申请一下，这个月送您XX元话费/加送XX流量…"</text>
  
  <!-- 核心策略 -->
  <rect x="200" y="950" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">核心策略</text>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">算价值账，不算价格账</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让用户看到物超所值的服务体验</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="400" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="400" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="700" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="700" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
