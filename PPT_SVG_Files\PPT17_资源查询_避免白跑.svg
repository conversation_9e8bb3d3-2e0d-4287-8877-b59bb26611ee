<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .method-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .color-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
      .special-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .warning-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">善用工具，避免"白跑一趟"</text>
  
  <!-- 方法1：GIS资源图层 -->
  <rect x="100" y="130" width="1720" height="300" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="180" class="method-title" fill="#3B82F6">方法1：查询GIS资源图层 (核心)</text>
  <text x="150" y="230" class="step-text">怎么看？ 输入地址，看颜色：</text>
  
  <!-- 颜色说明 -->
  <rect x="200" y="260" width="450" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <circle cx="250" cy="300" r="20" fill="#059669"/>
  <text x="290" y="290" class="color-text" fill="#059669">绿色 = 大概率OK</text>
  <text x="290" y="320" class="step-text">覆盖良好，可以放心接单</text>
  
  <rect x="680" y="260" width="450" height="120" fill="#FEF3C7" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <circle cx="730" cy="300" r="20" fill="#F59E0B"/>
  <text x="770" y="290" class="color-text" fill="#F59E0B">黄色 = 可能有点远，要小心</text>
  <text x="770" y="320" class="step-text">需要现场确认，做好预案</text>
  
  <rect x="1160" y="260" width="450" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <circle cx="1210" cy="300" r="20" fill="#DC2626"/>
  <text x="1250" y="290" class="color-text" fill="#DC2626">红色 = 覆盖困难</text>
  <text x="1250" y="320" class="step-text">需特殊处理，谨慎接单</text>
  
  <!-- 方法2：端口状态 -->
  <rect x="100" y="460" width="1720" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="510" class="method-title" fill="#059669">方法2：查看端口状态</text>
  <text x="150" y="560" class="step-text">通过系统或支撑人员确认该区域是否有空闲端口。</text>
  <text x="150" y="590" class="step-text">端口满了 = 装不了，需要扩容或等待释放。</text>
  
  <!-- 特殊情况处理 -->
  <rect x="100" y="640" width="1720" height="200" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  <text x="150" y="690" class="method-title" fill="#F59E0B">特殊情况处理：</text>
  <text x="150" y="740" class="special-text">遇到黄色/红色区域、端口不足、或山区/老旧小区等复杂情况，</text>
  <text x="150" y="780" class="warning-text">务必提前上报给后台支撑或班组长，共同确认方案！</text>
  <text x="150" y="820" class="warning-text">不能自作主张。</text>
  
  <!-- 底部提醒 -->
  <rect x="200" y="880" width="1520" height="120" fill="#FEF2F2" rx="20" stroke="#DC2626" stroke-width="3"/>
  <text x="960" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #DC2626; text-anchor: middle;">⚠️ 重要提醒</text>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #7F1D1D; text-anchor: middle;">装机前的"看一看"和"问一问"非常重要！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="280" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="280" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="535" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="535" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="740" r="35" fill="#F59E0B" opacity="0.3"/>
  <circle cx="1870" cy="740" r="35" fill="#F59E0B" opacity="0.3"/>
</svg>
