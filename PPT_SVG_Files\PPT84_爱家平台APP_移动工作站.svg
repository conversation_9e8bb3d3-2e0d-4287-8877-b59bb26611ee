<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .advantage-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .requirement-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 80px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">神器在手，工作不愁</text>
  <text x="960" y="140" class="subtitle-text">爱家平台(APP)：你的移动工作站</text>
  
  <!-- 主图标 -->
  <text x="960" y="280" class="icon-text">📱</text>
  
  <!-- 定位 -->
  <rect x="100" y="320" width="1720" height="150" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="370" class="section-title" fill="#3B82F6">定位：</text>
  <text x="150" y="410" class="content-text">它不只是个APP，更是帮助我们高效完成任务、发现营销机会、</text>
  <text x="150" y="440" class="content-text">获取工作支撑的重要工具！</text>
  
  <!-- 核心优势 -->
  <rect x="100" y="500" width="1720" height="300" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="550" class="section-title" fill="#059669">核心优势：</text>
  
  <!-- 优势展示 -->
  <rect x="200" y="590" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="340" y="635" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">信息查询</text>
  
  <text x="510" y="630" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="550" y="590" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="690" y="635" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">任务接收</text>
  
  <text x="860" y="630" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="900" y="590" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1040" y="635" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">工单处理</text>
  
  <text x="1210" y="630" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="1250" y="590" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1390" y="635" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">营销支持</text>
  
  <rect x="200" y="690" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="340" y="735" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">数据查看</text>
  
  <text x="510" y="730" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="550" y="690" width="730" height="80" fill="#E0F2FE" rx="10"/>
  <text x="915" y="735" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #0284C7; text-anchor: middle;">一站式搞定！</text>
  
  <!-- 功能价值 -->
  <rect x="200" y="830" width="1520" height="100" fill="#FEF7ED" rx="15"/>
  <text x="220" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">💡 功能价值：</text>
  <text x="220" y="900" class="advantage-text">随时随地掌握工作动态，及时响应用户需求，高效完成各项任务</text>
  
  <!-- 要求 -->
  <rect x="200" y="950" width="1520" height="100" fill="#F3E8FF" rx="20"/>
  <text x="960" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">要求</text>
  <text x="960" y="1010" class="requirement-text" fill="#1E3A8A">必须熟练掌握核心功能！</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">工欲善其事，必先利其器</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="395" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="395" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#059669" opacity="0.3"/>
</svg>
