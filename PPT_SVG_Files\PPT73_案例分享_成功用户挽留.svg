<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .story-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .story-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .insight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">真诚服务，留住用户的心</text>
  <text x="960" y="140" class="subtitle-text">案例分享：一次成功的用户挽留</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">💝</text>
  
  <!-- 故事背景 -->
  <rect x="100" y="300" width="1720" height="500" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="350" class="story-title" fill="#3B82F6">故事：</text>
  
  <!-- 案例内容 -->
  <rect x="200" y="390" width="1520" height="380" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="430" class="story-text">张师傅接到系统预警：李女士（高价值用户）连续投诉网络问题，流失风险极高。</text>
  
  <text x="220" y="480" class="story-text">🔍 <tspan font-weight="bold" fill="#DC2626">倾听了解</tspan>：张师傅第一时间上门，耐心听李女士抱怨网络卡顿影响孩子上网课。</text>
  
  <text x="220" y="530" class="story-text">🔧 <tspan font-weight="bold" fill="#059669">专业检测</tspan>：现场用爱家APP检测，发现WiFi信道拥堵严重，路由器位置不佳。</text>
  
  <text x="220" y="580" class="story-text">💡 <tspan font-weight="bold" fill="#EA580C">解决方案</tspan>：调整路由器位置，优化信道设置，推荐Mesh组网彻底解决覆盖问题。</text>
  
  <text x="220" y="630" class="story-text">🎁 <tspan font-weight="bold" fill="#7C3AED">贴心补偿</tspan>：申请免费Mesh体验1个月，赠送50元话费作为歉意。</text>
  
  <text x="220" y="680" class="story-text">📞 <tspan font-weight="bold" fill="#F59E0B">跟进服务</tspan>：一周后主动回访，确认网络稳定，用户非常满意。</text>
  
  <text x="220" y="730" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">结果：李女士不仅没有流失，还主动续约了更高档套餐，并推荐了3个邻居办理业务！</text>
  
  <!-- 启示 -->
  <rect x="100" y="830" width="1720" height="180" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="880" class="story-title" fill="#059669">启示：</text>
  <text x="960" y="920" class="insight-text" fill="#1E3A8A">专业的技能 + 真诚的态度 = 最好的挽留武器</text>
  
  <!-- 成功要素 -->
  <rect x="200" y="950" width="1520" height="50" fill="#DCFCE7" rx="15"/>
  <text x="220" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669;">成功要素：快速响应 + 专业诊断 + 有效解决 + 贴心补偿 + 持续跟进</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="550" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="550" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="920" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="920" r="25" fill="#059669" opacity="0.3"/>
</svg>
