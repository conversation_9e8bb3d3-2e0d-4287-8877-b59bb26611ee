<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">WiFi信道也"塞车"？换条路试试</text>
  <text x="960" y="140" class="subtitle-text">WiFi优化进阶：调整信道避开"堵车"</text>
  
  <!-- 原理说明 -->
  <rect x="100" y="180" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">原理：</text>
  <text x="150" y="280" class="content-text">周围邻居WiFi太多，大家挤在常用信道（如1, 6, 11）会互相干扰</text>
  <text x="150" y="320" class="highlight-text" fill="#DC2626">就像高峰期的马路，换个车道可能更顺畅！</text>
  <text x="150" y="360" class="step-text">5G WiFi干扰一般较小，主要优化2.4G信道</text>
  
  <!-- 操作方法 -->
  <rect x="100" y="410" width="1720" height="400" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="460" class="section-title" fill="#059669">方法：</text>
  
  <!-- 步骤1：检测 -->
  <rect x="200" y="500" width="480" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="440" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">1️⃣ 检测信道</text>
  <text x="220" y="580" class="step-text">使用爱家APP或WiFi分析工具</text>
  <text x="220" y="610" class="step-text">查看信道占用情况</text>
  <text x="220" y="640" class="step-text">找出拥堵的信道</text>
  
  <!-- 步骤2：设置 -->
  <rect x="720" y="500" width="480" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="960" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">2️⃣ 手动设置</text>
  <text x="740" y="580" class="step-text">登录路由器管理后台</text>
  <text x="740" y="610" class="step-text">或请用户操作</text>
  <text x="740" y="640" class="step-text">选择相对空闲的信道</text>
  
  <!-- 步骤3：验证 -->
  <rect x="1240" y="500" width="480" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1480" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">3️⃣ 效果验证</text>
  <text x="1260" y="580" class="step-text">重新测试网速和信号</text>
  <text x="1260" y="610" class="step-text">对比优化前后效果</text>
  <text x="1260" y="640" class="step-text">确认改善情况</text>
  
  <!-- 推荐信道 -->
  <rect x="200" y="680" width="1520" height="100" fill="#E0F2FE" rx="15"/>
  <text x="220" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #0284C7;">📊 推荐信道：</text>
  <text x="220" y="750" class="content-text">2.4G WiFi优选信道：1、6、11（互不干扰的三个信道）</text>
  <text x="220" y="775" class="step-text">根据现场检测结果，选择占用设备最少的信道</text>
  
  <!-- 注意事项 -->
  <rect x="100" y="820" width="1720" height="150" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <text x="150" y="870" class="section-title" fill="#EA580C">注意事项：</text>
  <text x="200" y="910" class="step-text">• 不是所有路由器都支持手动信道设置</text>
  <text x="200" y="940" class="step-text">• 部分老旧设备可能需要重启才能生效</text>
  <text x="200" y="970" class="step-text">• 如果用户不会操作，可以代为设置</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="1000" width="1520" height="60" fill="#F8FAFC" rx="15"/>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">小技巧解决大问题，专业服务体现价值！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="280" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="280" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="575" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="575" r="25" fill="#059669" opacity="0.3"/>
  
  <!-- 信号图标装饰 -->
  <text x="50" y="900" class="icon-text">📶</text>
  <text x="1870" y="900" class="icon-text">📶</text>
</svg>
