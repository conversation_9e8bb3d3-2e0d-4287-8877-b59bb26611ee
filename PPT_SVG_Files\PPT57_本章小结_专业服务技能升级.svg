<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .point-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">专业服务，技能升级</text>
  <text x="960" y="140" class="subtitle-text">本章小结：专业服务，技能升级</text>
  
  <!-- 回顾内容 -->
  <rect x="100" y="180" width="1720" height="700" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">回顾：</text>
  
  <!-- 五个一标准 -->
  <rect x="200" y="270" width="700" height="150" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="550" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">"五个一"标准要牢记</text>
  <text x="220" y="350" class="point-text">• 一套工具：标准配置，规范使用</text>
  <text x="220" y="380" class="point-text">• 一次测速：数据说话，建立信任</text>
  <text x="220" y="410" class="point-text">• 一份指引、一键验收、一句关怀</text>
  
  <!-- 常用工具 -->
  <rect x="950" y="270" width="700" height="150" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="1300" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">常用工具要熟练</text>
  <text x="970" y="350" class="point-text">• 光功率计、光纤处理、水晶头制作</text>
  <text x="970" y="380" class="point-text">• 爱家APP检测功能</text>
  <text x="970" y="410" class="point-text">• 专业工具规范操作</text>
  
  <!-- 投诉应对 -->
  <rect x="200" y="440" width="700" height="150" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="550" y="480" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #EA580C; text-anchor: middle;">投诉应对有方法</text>
  <text x="220" y="520" class="point-text">• 四步法：倾听→核实→方案→跟进</text>
  <text x="220" y="550" class="point-text">• 先情后理，共情沟通</text>
  <text x="220" y="580" class="point-text">• 标准话术灵活运用</text>
  
  <!-- WiFi优化 -->
  <rect x="950" y="440" width="700" height="150" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="1300" y="480" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">WiFi优化懂推荐</text>
  <text x="970" y="520" class="point-text">• 基础优化技巧</text>
  <text x="970" y="550" class="point-text">• Mesh组网适用场景</text>
  <text x="970" y="580" class="point-text">• 随销机会把握</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="610" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">核心：</text>
  <text x="960" y="690" class="core-text" fill="#059669">用专业技能和沟通技巧武装自己</text>
  <text x="960" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">技术过硬 + 服务贴心 + 沟通有效 = 用户满意</text>
  
  <!-- 成长路径 -->
  <rect x="200" y="750" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="780" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">成长路径</text>
  
  <!-- 成长步骤 -->
  <rect x="250" y="800" width="200" height="30" fill="#DCFCE7" rx="5"/>
  <text x="350" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">掌握标准</text>
  
  <text x="480" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="520" y="800" width="200" height="30" fill="#DCFCE7" rx="5"/>
  <text x="620" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">熟练工具</text>
  
  <text x="750" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="790" y="800" width="200" height="30" fill="#DCFCE7" rx="5"/>
  <text x="890" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">提升沟通</text>
  
  <text x="1020" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="1060" y="800" width="200" height="30" fill="#DCFCE7" rx="5"/>
  <text x="1160" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">增值服务</text>
  
  <text x="1290" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="1330" y="800" width="200" height="30" fill="#DCFCE7" rx="5"/>
  <text x="1430" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">专业服务</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="910" width="1520" height="100" fill="#EFF6FF" rx="20"/>
  <text x="960" y="940" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">💡 目标</text>
  <text x="960" y="970" class="highlight-text" fill="#059669" text-anchor="middle">从"合格"到"优秀"，成为用户信赖的专业服务人员</text>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">每一次服务都是展示专业能力的机会</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill: #059669" opacity="0.3"/>
  <circle cx="50" cy="550" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="550" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
