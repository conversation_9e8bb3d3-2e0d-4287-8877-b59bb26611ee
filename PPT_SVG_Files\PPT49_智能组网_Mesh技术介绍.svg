<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .feature-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">Mesh组网：告别WiFi死角的神器</text>
  <text x="960" y="140" class="subtitle-text">认识智能组网：Mesh技术</text>
  
  <!-- 什么是Mesh -->
  <rect x="100" y="180" width="1720" height="150" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">是什么？</text>
  <text x="150" y="280" class="content-text">由多个路由器（一个主路由+多个子路由）组成一个网络</text>
  <text x="150" y="320" class="highlight-text" fill="#059669">用简洁的拓扑图展示：多点覆盖，智能连接</text>
  
  <!-- Mesh网络拓扑图 -->
  <rect x="200" y="360" width="1520" height="200" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="2"/>
  <text x="960" y="390" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">Mesh网络拓扑示意图</text>
  
  <!-- 主路由 -->
  <circle cx="400" cy="460" r="40" fill="#3B82F6"/>
  <text x="400" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">主路由</text>
  <text x="400" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; text-anchor: middle;">客厅</text>
  
  <!-- 子路由1 -->
  <circle cx="800" cy="420" r="35" fill="#059669"/>
  <text x="800" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: white; text-anchor: middle;">子路由1</text>
  <text x="800" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">主卧</text>
  
  <!-- 子路由2 -->
  <circle cx="800" cy="500" r="35" fill="#059669"/>
  <text x="800" y="510" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: white; text-anchor: middle;">子路由2</text>
  <text x="800" y="550" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">书房</text>
  
  <!-- 子路由3 -->
  <circle cx="1200" cy="460" r="35" fill="#059669"/>
  <text x="1200" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; font-weight: bold; fill: white; text-anchor: middle;">子路由3</text>
  <text x="1200" y="510" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">次卧</text>
  
  <!-- 连接线 -->
  <path d="M 440 460 L 765 430" fill="none" stroke="#6B7280" stroke-width="3" stroke-dasharray="5,5"/>
  <path d="M 440 460 L 765 490" fill="none" stroke="#6B7280" stroke-width="3" stroke-dasharray="5,5"/>
  <path d="M 835 430 L 1165 450" fill="none" stroke="#6B7280" stroke-width="3" stroke-dasharray="5,5"/>
  <path d="M 835 490 L 1165 470" fill="none" stroke="#6B7280" stroke-width="3" stroke-dasharray="5,5"/>
  
  <!-- 优点介绍 -->
  <rect x="100" y="590" width="1720" height="350" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="640" class="section-title" fill="#059669">优点：</text>
  
  <!-- 覆盖广 -->
  <rect x="200" y="680" width="480" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="440" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">覆盖广</text>
  <text x="220" y="750" class="feature-text">轻松覆盖大户型、</text>
  <text x="220" y="780" class="feature-text">复杂户型</text>
  
  <!-- 无缝漫游 -->
  <rect x="720" y="680" width="480" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="960" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">无缝漫游</text>
  <text x="740" y="750" class="feature-text">手机走到哪，自动切换到</text>
  <text x="740" y="780" class="feature-text">信号最好的路由，不中断</text>
  
  <!-- 部署简单 -->
  <rect x="1240" y="680" width="480" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1480" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">部署相对简单</text>
  <text x="1260" y="750" class="feature-text">子路由插电即可</text>
  <text x="1260" y="780" class="feature-text">(部分支持无线回程)</text>
  
  <!-- 成本说明 -->
  <rect x="200" y="820" width="1520" height="80" fill="#E0F2FE" rx="15"/>
  <text x="220" y="860" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #0284C7;">💰 成本：</text>
  <text x="220" y="885" class="content-text">比单个路由器贵，但效果好，适合对网络体验要求高的用户</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="930" width="1520" height="100" fill="#FFFBEB" rx="20"/>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 核心价值</text>
  <text x="960" y="990" class="highlight-text" fill="#059669" text-anchor="middle">解决大户型WiFi覆盖难题的最佳方案</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">一次投资，全屋无死角</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="255" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="255" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="740" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="740" r="25" fill="#059669" opacity="0.3"/>
</svg>
