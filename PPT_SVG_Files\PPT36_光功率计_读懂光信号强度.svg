<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .error-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; }
      .error-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #7F1D1D; }
      .demo-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">光功率计：读懂光信号强度</text>
  <text x="960" y="140" class="subtitle-text">工具实战1：光功率计的正确使用</text>
  
  <!-- 操作步骤 -->
  <rect x="100" y="180" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">标准操作步骤</text>
  
  <!-- 步骤1 -->
  <rect x="200" y="280" width="700" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <circle cx="280" cy="340" r="30" fill="#3B82F6"/>
  <text x="280" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">1</text>
  <text x="330" y="320" class="step-title" fill="#3B82F6">步骤1：清洁准备</text>
  <text x="330" y="360" class="step-text">清洁光纤端面和仪表接口</text>
  <text x="330" y="385" class="step-text">确保接触面干净无污染</text>
  
  <!-- 步骤2 -->
  <rect x="950" y="280" width="700" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <circle cx="1030" cy="340" r="30" fill="#059669"/>
  <text x="1030" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">2</text>
  <text x="1080" y="320" class="step-title" fill="#059669">步骤2：选择波长</text>
  <text x="1080" y="360" class="step-text">选择正确的波长</text>
  <text x="1080" y="385" class="step-text">(一般是1490nm接收)</text>
  
  <!-- 步骤3 -->
  <rect x="200" y="420" width="700" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <circle cx="280" cy="480" r="30" fill="#EA580C"/>
  <text x="280" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">3</text>
  <text x="330" y="460" class="step-title" fill="#EA580C">步骤3：连接测量</text>
  <text x="330" y="500" class="step-text">连接光纤跳线</text>
  <text x="330" y="525" class="step-text">读取Rx Power值</text>
  
  <!-- 步骤4 -->
  <rect x="950" y="420" width="700" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <circle cx="1030" cy="480" r="30" fill="#7C3AED"/>
  <text x="1030" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">4</text>
  <text x="1080" y="460" class="step-title" fill="#7C3AED">步骤4：判断读数</text>
  <text x="1080" y="500" class="step-text">判断读数是否在正常范围</text>
  <text x="1080" y="525" class="step-text">(≤ -27dBm)</text>
  
  <!-- 读数示例 -->
  <rect x="300" y="570" width="1320" height="120" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <text x="960" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">📊 读数示例</text>
  <text x="960" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">正常：-25.3 dBm ✓　　异常：-30.5 dBm ✗　　过强：-5.2 dBm ⚠️</text>
  <text x="960" y="680" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">数值越接近0越好，但不能过强</text>
  
  <!-- 常见错误 -->
  <rect x="100" y="810" width="1720" height="150" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <text x="150" y="860" class="error-title">常见错误：</text>
  <text x="200" y="900" class="error-text">❌ 接口不干净读数不准</text>
  <text x="600" y="900" class="error-text">❌ 波长选错</text>
  <text x="900" y="900" class="error-text">❌ 跳线本身有问题</text>
  <text x="200" y="930" class="error-text">❌ 连接不牢固</text>
  <text x="600" y="930" class="error-text">❌ 设备未校准</text>
  <text x="900" y="930" class="error-text">❌ 环境光干扰</text>
  
  <!-- 现场演示提示 -->
  <rect x="200" y="980" width="1520" height="80" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1010" class="demo-text">🎥 现场演示/视频：演示测量过程</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">实际操作比理论更重要，多练习才能熟练掌握！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="630" r="25" fill="#F59E0B" opacity="0.3"/>
  <circle cx="1870" cy="630" r="25" fill="#F59E0B" opacity="0.3"/>
</svg>
