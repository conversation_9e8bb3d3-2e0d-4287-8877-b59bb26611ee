<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; }
      .review-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .key-point { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #059669; text-anchor: middle; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">把好入网每道关</text>
  
  <!-- 回顾区域 -->
  <rect x="100" y="130" width="1720" height="700" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <circle cx="200" cy="200" r="40" fill="#3B82F6"/>
  <text x="200" y="215" class="icon-text" fill="white">📋</text>
  <text x="280" y="180" class="section-title" fill="#3B82F6">回顾：</text>
  
  <!-- 装前多看一步 -->
  <rect x="200" y="240" width="1480" height="100" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="240" y="280" class="key-point" fill="#3B82F6">装前多看一步 (资源)：</text>
  <text x="240" y="320" class="review-text">• GIS资源图层查询，看颜色判断覆盖情况</text>
  <text x="600" y="320" class="review-text">• 端口状态确认，避免白跑一趟</text>
  
  <!-- 装中紧盯四指标 -->
  <rect x="200" y="360" width="1480" height="140" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="240" y="400" class="key-point" fill="#059669">装中紧盯四指标 (光衰/速率/熔接/标签)：</text>
  <text x="240" y="440" class="review-text">• 光衰值 ≤ -27dBm，确保信号质量</text>
  <text x="240" y="480" class="review-text">• WiFi速率 ≥ 套餐带宽90%，兑现承诺</text>
  <text x="800" y="440" class="review-text">• 熔接质量稳定可靠，细节决定成败</text>
  <text x="800" y="480" class="review-text">• 标签规范完整，方便后续维护</text>
  
  <!-- 装后走好五步 -->
  <rect x="200" y="520" width="1480" height="140" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="240" y="560" class="key-point" fill="#EA580C">装后走好五步 (验收流程)：</text>
  <text x="240" y="600" class="review-text">• 服务复核 → 性能演示 → 功能介绍 → 电子签收 → 温馨提示</text>
  <text x="240" y="640" class="review-text">• 每一步都不能省，规范操作保护自己也让用户放心</text>
  
  <!-- AI质检助力 -->
  <rect x="200" y="680" width="1480" height="100" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="240" y="720" class="key-point" fill="#7C3AED">AI质检助力：</text>
  <text x="240" y="760" class="review-text">• 智能识别线路整理、标签规范、工具摆放，提醒注意细节</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="860" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="900" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">核心：</text>
  <text x="960" y="950" class="core-text">标准化 + 责任心 = 高质量入网</text>
  
  <!-- 流程图示 -->
  <rect x="100" y="1000" width="1720" height="60" fill="#EFF6FF" rx="15"/>
  
  <!-- 流程箭头 -->
  <circle cx="200" cy="1030" r="20" fill="#3B82F6"/>
  <text x="200" y="1038" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">看</text>
  
  <path d="M 220 1030 L 280 1030" fill="none" stroke="#3B82F6" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <circle cx="300" cy="1030" r="20" fill="#059669"/>
  <text x="300" y="1038" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">装</text>
  
  <path d="M 320 1030 L 380 1030" fill="none" stroke="#3B82F6" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <circle cx="400" cy="1030" r="20" fill="#EA580C"/>
  <text x="400" y="1038" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">验</text>
  
  <path d="M 420 1030 L 480 1030" fill="none" stroke="#3B82F6" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <circle cx="500" cy="1030" r="20" fill="#7C3AED"/>
  <text x="500" y="1038" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 16px; font-weight: bold; fill: white; text-anchor: middle;">AI</text>
  
  <text x="600" y="1038" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A;">= 高质量入网</text>
  
  <!-- 装饰元素 -->
  <circle cx="1650" cy="300" r="50" fill="#E0F2FE" opacity="0.6"/>
  <circle cx="1750" cy="450" r="40" fill="#DCFCE7" opacity="0.5"/>
  <circle cx="1650" cy="600" r="45" fill="#FED7AA" opacity="0.6"/>
  <circle cx="1750" cy="730" r="35" fill="#E0E7FF" opacity="0.5"/>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6"/>
    </marker>
  </defs>
</svg>
