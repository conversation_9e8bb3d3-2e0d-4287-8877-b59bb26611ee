<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .room-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; text-anchor: middle; }
      .analysis-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .solution-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .instruction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">看图识户型，精准定方案</text>
  <text x="960" y="140" class="subtitle-text">互动练习：户型分析实战</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">🏗️</text>
  
  <!-- 户型图区域 -->
  <rect x="100" y="300" width="800" height="500" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="3"/>
  <text x="500" y="340" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">户型图示例</text>
  
  <!-- 模拟户型图 -->
  <rect x="150" y="370" width="700" height="400" fill="white" rx="10" stroke="#3B82F6" stroke-width="2"/>
  
  <!-- 客厅 -->
  <rect x="200" y="420" width="200" height="150" fill="#E0F2FE" rx="5" stroke="#0284C7" stroke-width="1"/>
  <text x="300" y="500" class="room-text">客厅</text>
  <circle cx="250" cy="450" r="8" fill="#DC2626"/>
  <text x="250" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 12px; fill: #DC2626; text-anchor: middle;">光猫</text>
  
  <!-- 主卧 -->
  <rect x="450" y="420" width="150" height="120" fill="#DCFCE7" rx="5" stroke="#059669" stroke-width="1"/>
  <text x="525" y="485" class="room-text">主卧</text>
  
  <!-- 次卧 -->
  <rect x="650" y="420" width="150" height="120" fill="#DCFCE7" rx="5" stroke="#059669" stroke-width="1"/>
  <text x="725" y="485" class="room-text">次卧</text>
  
  <!-- 书房 -->
  <rect x="450" y="580" width="150" height="120" fill="#FED7AA" rx="5" stroke="#EA580C" stroke-width="1"/>
  <text x="525" y="645" class="room-text">书房</text>
  
  <!-- 厨房 -->
  <rect x="200" y="620" width="100" height="80" fill="#E0E7FF" rx="5" stroke="#6366F1" stroke-width="1"/>
  <text x="250" y="665" class="room-text">厨房</text>
  
  <!-- 卫生间 -->
  <rect x="650" y="580" width="150" height="120" fill="#F3E8FF" rx="5" stroke="#7C3AED" stroke-width="1"/>
  <text x="725" y="645" class="room-text">卫生间</text>
  
  <!-- 墙体标识 -->
  <rect x="400" y="420" width="10" height="280" fill="#6B7280"/>
  <rect x="200" y="570" width="400" height="10" fill="#6B7280"/>
  
  <!-- 分析区域 -->
  <rect x="950" y="300" width="870" height="500" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="3"/>
  <text x="1385" y="340" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">分析要点</text>
  
  <!-- 户型特点 -->
  <rect x="1000" y="370" width="770" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="1020" y="400" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6;">户型特点：</text>
  <text x="1020" y="430" class="analysis-text">• 三室两厅，约120平米</text>
  <text x="1020" y="460" class="analysis-text">• L型布局，有承重墙阻挡</text>
  
  <!-- 覆盖难点 -->
  <rect x="1000" y="510" width="770" height="120" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1020" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #DC2626;">覆盖难点：</text>
  <text x="1020" y="570" class="analysis-text">• 主卧、次卧距离光猫较远</text>
  <text x="1020" y="600" class="analysis-text">• 书房被墙体阻挡，信号衰减严重</text>
  
  <!-- 推荐方案 -->
  <rect x="1000" y="650" width="770" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1020" y="680" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669;">推荐方案：</text>
  <text x="1020" y="710" class="solution-text">Mesh组网：客厅主路由 + 主卧子路由 + 书房子路由</text>
  <text x="1020" y="740" class="analysis-text">实现全屋无死角覆盖</text>
  
  <!-- 练习要求 -->
  <rect x="200" y="830" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="860" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 练习要求</text>
  <text x="220" y="890" class="instruction-text">1. 分析户型特点和覆盖难点</text>
  <text x="220" y="920" class="instruction-text">2. 选择最适合的组网方案并说明理由</text>
  <text x="220" y="950" class="instruction-text">3. 考虑成本效益和用户接受度</text>
  
  <!-- 底部提示 -->
  <rect x="200" y="970" width="1520" height="80" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">实战技巧</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280; text-anchor: middle;">看户型图 → 识别难点 → 选择方案 → 专业推荐</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="550" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="550" r="30" fill="#059669" opacity="0.3"/>
</svg>
