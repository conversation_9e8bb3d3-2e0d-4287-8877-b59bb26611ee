<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .tool-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .action-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">好印象加分项：专业物料不可少</text>
  <text x="960" y="140" class="subtitle-text">品牌传递触点2：善用宣传"小道具"</text>
  
  <!-- 工具展示 -->
  <rect x="100" y="180" width="1720" height="650" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">工具：</text>
  
  <!-- 爱家服务手册 -->
  <rect x="200" y="280" width="480" height="180" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="440" y="320" class="tool-title" fill="#3B82F6">《爱家服务手册/单页》</text>
  <text x="220" y="360" class="tool-text">服务过程中或结束时递交给用户，</text>
  <text x="220" y="390" class="tool-text">供其了解爱家服务内容</text>
  <text x="220" y="420" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6;">📖 [图片] 精美宣传册</text>
  <text x="220" y="445" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">让用户带回家慢慢了解</text>
  
  <!-- 爱家APP演示 -->
  <rect x="720" y="280" width="480" height="180" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="960" y="320" class="tool-title" fill="#059669">爱家APP演示</text>
  <text x="740" y="360" class="tool-text">现场演示APP的便捷功能</text>
  <text x="740" y="390" class="tool-text">(如WiFi管理、一键报障)</text>
  <text x="740" y="420" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669;">📱 [截图] 功能界面</text>
  <text x="740" y="445" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">让用户亲眼看到便利性</text>
  
  <!-- 品牌Logo魔术贴 -->
  <rect x="1240" y="280" width="480" height="180" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="1480" y="320" class="tool-title" fill="#EA580C">品牌Logo魔术贴</text>
  <text x="1260" y="360" class="tool-text">安装结束后，按规范粘贴在</text>
  <text x="1260" y="390" class="tool-text">光猫/路由器上</text>
  <text x="1260" y="420" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C;">🏷️ [图片] 品牌标识</text>
  <text x="1260" y="445" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">持续的品牌提醒</text>
  
  <!-- 使用场景 -->
  <rect x="200" y="480" width="1520" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="960" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">使用场景</text>
  <text x="220" y="550" class="tool-text">安装调试时展示手册 → 现场演示APP功能 → 完工后粘贴标识</text>
  <text x="220" y="580" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280;">全流程品牌展示，加深用户印象</text>
  
  <!-- 动作要求 -->
  <rect x="200" y="620" width="1520" height="120" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <text x="960" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">动作要求</text>
  <text x="220" y="690" class="tool-text">物料整洁、摆放规范、主动展示、拍照上传</text>
  <text x="220" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #F59E0B;">每一个细节都体现专业形象</text>
  
  <!-- 效果强调 -->
  <rect x="200" y="860" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="910" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">效果</text>
  <text x="960" y="950" class="action-text" fill="#1E3A8A">专业物料 + 规范展示 = 品牌形象提升</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让用户感受到移动爱家的专业和用心</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="680" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="680" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
