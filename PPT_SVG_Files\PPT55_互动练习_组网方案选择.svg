<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .case-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .case-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .option-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; }
      .instruction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">实战演练，方案选择</text>
  <text x="960" y="140" class="subtitle-text">互动练习：组网方案选择</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">🏠</text>
  
  <!-- 案例描述 -->
  <rect x="100" y="300" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="350" class="case-title" fill="#3B82F6">案例：王先生家的网络困扰</text>
  <text x="150" y="390" class="case-text">• 户型：140平米，三室两厅，L型布局</text>
  <text x="150" y="420" class="case-text">• 现状：客厅光猫+路由器，主卧信号弱，书房经常掉线</text>
  <text x="150" y="450" class="case-text">• 需求：全家WiFi信号稳定，支持4K电视、在线办公、孩子上网课</text>
  <text x="150" y="480" class="case-text">• 预算：愿意投资改善网络体验，但希望性价比高</text>
  
  <!-- 方案选项 -->
  <rect x="100" y="530" width="1720" height="400" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="3"/>
  <text x="960" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">请选择最佳方案</text>
  
  <!-- 方案A -->
  <rect x="200" y="600" width="700" height="100" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="220" y="630" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626;">方案A：基础优化</text>
  <text x="220" y="660" class="option-text">调整现有路由器位置，优化信道设置</text>
  <text x="220" y="685" class="option-text">成本：0元，效果有限</text>
  
  <!-- 方案B -->
  <rect x="950" y="600" width="700" height="100" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="970" y="630" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">方案B：WiFi扩展器</text>
  <text x="970" y="660" class="option-text">在主卧和书房各放一个WiFi扩展器</text>
  <text x="970" y="685" class="option-text">成本：200-400元，信号有改善但可能不稳定</text>
  
  <!-- 方案C -->
  <rect x="200" y="720" width="700" height="100" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">方案C：Mesh组网</text>
  <text x="220" y="780" class="option-text">1个主路由+2个子路由，全屋无缝覆盖</text>
  <text x="220" y="805" class="option-text">成本：800-1200元，效果最佳</text>
  
  <!-- 方案D -->
  <rect x="950" y="720" width="700" height="100" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="970" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED;">方案D：电力猫组网</text>
  <text x="970" y="780" class="option-text">通过电力线传输网络信号</text>
  <text x="970" y="805" class="option-text">成本：300-600元，受电路影响较大</text>
  
  <!-- 分析要点 -->
  <rect x="200" y="850" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="880" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 分析要点</text>
  <text x="220" y="910" class="instruction-text">考虑因素：户型特点、覆盖需求、使用场景、预算范围、长期效果</text>
  <text x="220" y="940" class="instruction-text">推荐理由：需要结合用户实际情况，给出专业建议和性价比分析</text>
  
  <!-- 答案提示 -->
  <rect x="200" y="990" width="1520" height="70" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1015" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">推荐答案：方案C (Mesh组网)</text>
  <text x="960" y="1045" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #6B7280; text-anchor: middle;">理由：户型较大且复杂，对网络稳定性要求高，用户有一定预算，Mesh方案最适合</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="400" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="400" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="700" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="700" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
