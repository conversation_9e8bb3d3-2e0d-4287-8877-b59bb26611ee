<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .signal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">手机内存告急？推荐家庭云来帮忙</text>
  <text x="960" y="140" class="subtitle-text">"促升档"3：家庭云，数据存储新选择</text>
  
  <!-- 识别信号 -->
  <rect x="100" y="180" width="1720" height="250" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#DC2626">识别信号：</text>
  
  <!-- 信号展示 -->
  <rect x="200" y="270" width="480" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="440" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">存储不足</text>
  <text x="220" y="340" class="signal-text">用户抱怨手机存储</text>
  <text x="220" y="370" class="signal-text">空间不足</text>
  
  <rect x="720" y="270" width="480" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="960" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">备份需求</text>
  <text x="740" y="340" class="signal-text">需要备份孩子照片、</text>
  <text x="740" y="370" class="signal-text">重要文件</text>
  
  <rect x="1240" y="270" width="480" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1480" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">共享需求</text>
  <text x="1260" y="340" class="signal-text">需要和家人共享</text>
  <text x="1260" y="370" class="signal-text">照片、视频</text>
  
  <!-- 推荐话术 -->
  <rect x="100" y="460" width="1720" height="300" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="510" class="section-title" fill="#059669">推荐话术：</text>
  
  <rect x="200" y="550" width="1520" height="180" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="590" class="speech-text">"手机照片/视频太多存不下了吧？可以试试我们的家庭云，</text>
  <text x="220" y="620" class="speech-text">能自动备份手机里的照片视频，安全还不占地方。</text>
  <text x="220" y="650" class="speech-text">全家人都能一起用，随时随地查看分享。</text>
  <text x="220" y="680" class="speech-text">现在开通有免费体验/优惠活动…"</text>
  <text x="220" y="710" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #059669;">💡 结合现场：看到用户手机提示存储不足时主动推荐</text>
  
  <!-- 价值点突出 -->
  <rect x="100" y="790" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="840" class="section-title" fill="#3B82F6">突出价值点：</text>
  
  <!-- 三大价值 -->
  <rect x="200" y="880" width="480" height="80" fill="#DBEAFE" rx="10"/>
  <text x="440" y="930" class="value-text" fill="#3B82F6">安全备份</text>
  
  <text x="710" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="750" y="880" width="480" height="80" fill="#DBEAFE" rx="10"/>
  <text x="990" y="930" class="value-text" fill="#3B82F6">释放手机空间</text>
  
  <text x="1260" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="1300" y="880" width="420" height="80" fill="#DBEAFE" rx="10"/>
  <text x="1510" y="930" class="value-text" fill="#3B82F6">家庭共享</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="1000" width="1520" height="60" fill="#FFFBEB" rx="15"/>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">解决痛点，创造价值，提升用户数字生活品质！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="320" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="320" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#059669" opacity="0.3"/>
  
  <!-- 云存储图标装饰 -->
  <text x="50" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #3B82F6;">☁️</text>
  <text x="1870" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #3B82F6;">☁️</text>
</svg>
