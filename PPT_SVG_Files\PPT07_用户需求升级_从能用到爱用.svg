<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .stage-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: white; text-anchor: middle; }
      .stage-subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; }
      .quote-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; font-style: italic; }
      .need-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; }
      .arrow-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">满足用户不断升级的需求</text>
  
  <!-- 过去阶段 -->
  <rect x="80" y="150" width="480" height="350" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <rect x="80" y="150" width="480" height="80" fill="#DC2626" rx="25"/>
  <rect x="80" y="210" width="480" height="20" fill="#DC2626"/>
  <text x="320" y="205" class="stage-title">过去</text>
  
  <text x="120" y="280" class="stage-subtitle" fill="#DC2626">基础需求：连接</text>
  <text x="120" y="330" class="quote-text">"师傅，帮我把网</text>
  <text x="120" y="365" class="quote-text">装通就行。"</text>
  <text x="120" y="420" class="need-text">只要能上网就满足</text>
  <text x="120" y="450" class="need-text">对网络质量要求不高</text>
  
  <!-- 箭头1 -->
  <text x="640" y="340" class="arrow-text">→</text>
  
  <!-- 现在阶段 -->
  <rect x="720" y="150" width="480" height="350" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <rect x="720" y="150" width="480" height="80" fill="#EA580C" rx="25"/>
  <rect x="720" y="210" width="480" height="20" fill="#EA580C"/>
  <text x="960" y="205" class="stage-title">现在</text>
  
  <text x="760" y="280" class="stage-subtitle" fill="#EA580C">体验需求：高速稳定</text>
  <text x="760" y="320" class="quote-text">"我家WiFi信号要全覆盖，</text>
  <text x="760" y="355" class="quote-text">看4K电影不能卡，</text>
  <text x="760" y="390" class="quote-text">玩游戏延迟要低！"</text>
  <text x="760" y="440" class="need-text">高速、稳定、全屋智能</text>
  <text x="760" y="470" class="need-text">个性化体验要求</text>
  
  <!-- 箭头2 -->
  <text x="1280" y="340" class="arrow-text">→</text>
  
  <!-- 未来阶段 -->
  <rect x="1360" y="150" width="480" height="350" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <rect x="1360" y="150" width="480" height="80" fill="#059669" rx="25"/>
  <rect x="1360" y="210" width="480" height="20" fill="#059669"/>
  <text x="1600" y="205" class="stage-title">未来</text>
  
  <text x="1400" y="280" class="stage-subtitle" fill="#059669">情感需求：信赖推荐</text>
  <text x="1400" y="320" class="quote-text">"你们移动的服务真不错，</text>
  <text x="1400" y="355" class="quote-text">不仅网络好，还帮我解决</text>
  <text x="1400" y="390" class="quote-text">了摄像头的问题，我推荐</text>
  <text x="1400" y="425" class="quote-text">我邻居也装！"</text>
  <text x="1400" y="470" class="need-text">信赖、推荐、口碑传播</text>
  
  <!-- 底部总结 -->
  <rect x="200" y="580" width="1520" height="400" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="3"/>
  
  <!-- 需求升级图示 -->
  <circle cx="400" cy="700" r="80" fill="#DC2626" opacity="0.8"/>
  <text x="400" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">能用</text>
  
  <path d="M 480 700 L 560 700" fill="none" stroke="#3B82F6" stroke-width="8" marker-end="url(#arrowhead)"/>
  
  <circle cx="640" cy="700" r="80" fill="#EA580C" opacity="0.8"/>
  <text x="640" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">好用</text>
  
  <path d="M 720 700 L 800 700" fill="none" stroke="#3B82F6" stroke-width="8" marker-end="url(#arrowhead)"/>
  
  <circle cx="880" cy="700" r="80" fill="#059669" opacity="0.8"/>
  <text x="880" y="720" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">爱用</text>
  
  <!-- 关键启示 -->
  <text x="960" y="850" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">我们的服务必须跟上用户需求的升级步伐！</text>
  <text x="960" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: normal; fill: #059669; text-anchor: middle;">从满足基本需求到创造超预期体验</text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6"/>
    </marker>
  </defs>
</svg>
