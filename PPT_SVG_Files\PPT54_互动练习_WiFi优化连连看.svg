<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .scenario-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .solution-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; }
      .instruction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">学以致用，巩固知识</text>
  <text x="960" y="140" class="subtitle-text">互动练习：WiFi优化连连看</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">🎯</text>
  
  <!-- 练习说明 -->
  <rect x="100" y="300" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="340" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6;">练习说明：</text>
  <text x="150" y="380" class="instruction-text">根据不同用户场景，选择最合适的优化方法或组网方案</text>
  
  <!-- 场景区域 -->
  <rect x="100" y="430" width="850" height="500" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="3"/>
  <text x="525" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">用户场景</text>
  
  <!-- 场景1 -->
  <rect x="150" y="500" width="750" height="80" fill="#FEF2F2" rx="10" stroke="#DC2626" stroke-width="2"/>
  <text x="525" y="530" class="scenario-title" fill="#DC2626">A. 小户型单身公寓</text>
  <text x="170" y="560" class="scenario-text">60平米，一室一厅，WiFi偶尔卡顿</text>
  
  <!-- 场景2 -->
  <rect x="150" y="600" width="750" height="80" fill="#FEF7ED" rx="10" stroke="#EA580C" stroke-width="2"/>
  <text x="525" y="630" class="scenario-title" fill="#EA580C">B. 三室两厅大平层</text>
  <text x="170" y="660" class="scenario-text">120平米，主卧信号弱，书房经常掉线</text>
  
  <!-- 场景3 -->
  <rect x="150" y="700" width="750" height="80" fill="#F0FDF4" rx="10" stroke="#059669" stroke-width="2"/>
  <text x="525" y="730" class="scenario-title" fill="#059669">C. 复式别墅</text>
  <text x="170" y="760" class="scenario-text">200平米，三层，智能设备多，二三楼信号差</text>
  
  <!-- 场景4 -->
  <rect x="150" y="800" width="750" height="80" fill="#F3E8FF" rx="10" stroke="#7C3AED" stroke-width="2"/>
  <text x="525" y="830" class="scenario-title" fill="#7C3AED">D. 老旧小区</text>
  <text x="170" y="860" class="scenario-text">80平米，邻居WiFi多，干扰严重，网速不稳定</text>
  
  <!-- 解决方案区域 -->
  <rect x="970" y="430" width="850" height="500" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="3"/>
  <text x="1395" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">解决方案</text>
  
  <!-- 方案1 -->
  <rect x="1020" y="500" width="750" height="80" fill="#E0F2FE" rx="10" stroke="#0284C7" stroke-width="2"/>
  <text x="1395" y="530" class="scenario-title" fill="#0284C7">① 基础优化</text>
  <text x="1040" y="560" class="solution-text">调整路由器位置，优化天线角度，定期重启</text>
  
  <!-- 方案2 -->
  <rect x="1020" y="600" width="750" height="80" fill="#ECFDF5" rx="10" stroke="#10B981" stroke-width="2"/>
  <text x="1395" y="630" class="scenario-title" fill="#10B981">② 信道优化</text>
  <text x="1040" y="660" class="solution-text">检测信道占用，手动设置空闲信道</text>
  
  <!-- 方案3 -->
  <rect x="1020" y="700" width="750" height="80" fill="#FEF3C7" rx="10" stroke="#F59E0B" stroke-width="2"/>
  <text x="1395" y="730" class="scenario-title" fill="#F59E0B">③ WiFi扩展器</text>
  <text x="1040" y="760" class="solution-text">增加WiFi扩展器，扩大覆盖范围</text>
  
  <!-- 方案4 -->
  <rect x="1020" y="800" width="750" height="80" fill="#EDE9FE" rx="10" stroke="#8B5CF6" stroke-width="2"/>
  <text x="1395" y="830" class="scenario-title" fill="#8B5CF6">④ Mesh组网</text>
  <text x="1040" y="860" class="solution-text">部署Mesh路由器，实现全屋无缝覆盖</text>
  
  <!-- 连接提示 -->
  <rect x="200" y="960" width="1520" height="100" fill="#FFFBEB" rx="20"/>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 练习提示</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">请将左侧场景与右侧最合适的解决方案进行匹配</text>
  <text x="960" y="1050" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #6B7280; text-anchor: middle;">参考答案：A-①②  B-③  C-④  D-②</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="650" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="650" r="30" fill="#3B82F6" opacity="0.3"/>
</svg>
