<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .item-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; }
      .usage-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: bold; }
      .usage-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .value-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">数据看板：我的"工作导航仪"</text>
  <text x="960" y="140" class="subtitle-text">平台核心功能4：可视化看板 (一线能看懂的)</text>
  
  <!-- 能看到什么 -->
  <rect x="100" y="180" width="1720" height="400" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">能看到什么？ (展示简化版看板截图)</text>
  
  <!-- 我的工单 -->
  <rect x="200" y="280" width="480" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="440" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">我的工单</text>
  <text x="220" y="350" class="item-text">今日待办、已完成、</text>
  <text x="220" y="380" class="item-text">超时预警等</text>
  
  <!-- 我的业绩 -->
  <rect x="720" y="280" width="480" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="960" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">我的业绩（若有）</text>
  <text x="740" y="350" class="item-text">随销成果、NPS得分等</text>
  <text x="740" y="380" class="item-text">个人表现一目了然</text>
  
  <!-- 区域概况 -->
  <rect x="1240" y="280" width="480" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="1480" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C; text-anchor: middle;">区域概况</text>
  <text x="1480" y="335" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #EA580C; text-anchor: middle;">(简化热力图)</text>
  <text x="1260" y="365" class="item-text">大致了解负责区域的</text>
  <text x="1260" y="390" class="item-text">用户活跃度、网络告警情况</text>
  
  <!-- 数据价值 -->
  <rect x="200" y="420" width="1520" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="960" y="460" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">数据价值</text>
  <text x="220" y="490" class="item-text">让工作更有条理，让服务更有针对性，让成长更有方向</text>
  <text x="220" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">数据驱动决策，科学指导工作</text>
  
  <!-- 怎么用 -->
  <rect x="100" y="610" width="1720" height="300" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="150" y="660" class="section-title" fill="#1E3A8A">怎么用？</text>
  
  <!-- 管理工作 -->
  <rect x="200" y="700" width="480" height="80" fill="#DCFCE7" rx="10"/>
  <text x="220" y="730" class="usage-title" fill="#059669">管理工作：</text>
  <text x="220" y="755" class="usage-text">清晰了解任务进度，合理安排时间</text>
  
  <!-- 了解片区 -->
  <rect x="720" y="700" width="480" height="80" fill="#DCFCE7" rx="10"/>
  <text x="740" y="730" class="usage-title" fill="#059669">了解片区：</text>
  <text x="740" y="755" class="usage-text">对负责区域的情况心中有数</text>
  
  <!-- 发现问题 -->
  <rect x="1240" y="700" width="480" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1260" y="730" class="usage-title" fill="#059669">发现问题：</text>
  <text x="1260" y="755" class="usage-text">通过数据异常提前发现潜在问题</text>
  
  <!-- 使用建议 -->
  <rect x="200" y="800" width="1520" height="80" fill="#FFFBEB" rx="15"/>
  <text x="220" y="830" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #F59E0B;">使用建议：</text>
  <text x="220" y="855" class="usage-text">每日查看，定期分析，及时调整工作重点和方法</text>
  
  <!-- 核心价值 -->
  <rect x="200" y="920" width="1520" height="120" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">核心价值</text>
  <text x="960" y="990" class="value-text" fill="#1E3A8A">让数据成为工作指南针</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">用数据说话，用数据指导，用数据提升</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="750" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="750" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
