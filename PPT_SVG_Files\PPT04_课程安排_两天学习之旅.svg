<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .day-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: white; text-anchor: middle; }
      .day-subtitle { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #374151; }
      .module-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 6; opacity: 0.3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 0 150 Q 960 50 1920 150" class="accent-curve"/>
  
  <!-- 标题 -->
  <text x="960" y="120" class="title-text">精彩内容，不容错过</text>
  
  <!-- Day 1 区域 -->
  <rect x="80" y="200" width="800" height="700" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  
  <!-- Day 1 标题背景 -->
  <rect x="80" y="200" width="800" height="100" fill="#3B82F6" rx="25"/>
  <rect x="80" y="275" width="800" height="25" fill="#3B82F6"/>
  <text x="480" y="265" class="day-title">Day 1：质量筑基</text>
  
  <!-- Day 1 内容 -->
  <text x="120" y="360" class="day-subtitle">模块1：为何要做好服务质量？</text>
  <text x="140" y="410" class="module-text">• 家宽高质量发展核心逻辑</text>
  <text x="140" y="450" class="module-text">• 用户体验地图与NPS理解</text>
  <text x="140" y="490" class="module-text">• 质量指标与价值体现</text>
  
  <text x="120" y="560" class="day-subtitle">模块2：装机不出错：把好入网关</text>
  <text x="140" y="610" class="module-text">• 入网质量"三不原则"</text>
  <text x="140" y="650" class="module-text">• 施工核心指标掌握</text>
  <text x="140" y="690" class="module-text">• 用户验收标准流程</text>
  
  <text x="120" y="760" class="day-subtitle">模块3：专业范养成：安装服务标准化</text>
  <text x="140" y="810" class="module-text">• "五个一"服务标准</text>
  <text x="140" y="850" class="module-text">• 工具使用与沟通技巧</text>
  
  <!-- Day 2 区域 -->
  <rect x="1040" y="200" width="800" height="700" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  
  <!-- Day 2 标题背景 -->
  <rect x="1040" y="200" width="800" height="100" fill="#059669" rx="25"/>
  <rect x="1040" y="275" width="800" height="25" fill="#059669"/>
  <text x="1440" y="265" class="day-title">Day 2：价值深耕</text>
  
  <!-- Day 2 内容 -->
  <text x="1080" y="360" class="day-subtitle" fill="#059669">模块4：老用户维系：三促三控有妙招</text>
  <text x="1100" y="410" class="module-text">• 存量用户维护策略</text>
  <text x="1100" y="450" class="module-text">• 流失预警与挽留技巧</text>
  <text x="1100" y="490" class="module-text">• 增值业务推荐时机</text>
  
  <text x="1080" y="560" class="day-subtitle" fill="#059669">模块5：品牌与平台：讲好故事用好工具</text>
  <text x="1100" y="610" class="module-text">• 爱家APP功能深度应用</text>
  <text x="1100" y="650" class="module-text">• 品牌故事与价值传递</text>
  <text x="1100" y="690" class="module-text">• 智能组网推荐话术</text>
  
  <text x="1080" y="760" class="day-subtitle" fill="#059669">模块6：实战演练与行动启航</text>
  <text x="1100" y="810" class="module-text">• 场景模拟与角色扮演</text>
  <text x="1100" y="850" class="module-text">• 30天提升计划制定</text>
  
  <!-- 连接线装饰 -->
  <path d="M 880 550 Q 960 550 1040 550" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="10,5"/>
  <circle cx="960" cy="550" r="15" fill="#6B7280"/>
</svg>
