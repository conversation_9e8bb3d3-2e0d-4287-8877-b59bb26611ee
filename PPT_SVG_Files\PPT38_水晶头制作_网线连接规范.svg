<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .wire-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; text-anchor: middle; }
      .demo-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">网线连接：水晶头制作要点</text>
  <text x="960" y="140" class="subtitle-text">工具实战3：水晶头制作规范</text>
  
  <!-- 线序标准 -->
  <rect x="100" y="180" width="1720" height="250" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">线序标准：</text>
  <text x="150" y="280" class="content-text">牢记T568B线序（标准网线接法）</text>
  
  <!-- 线序图示 -->
  <rect x="200" y="320" width="1520" height="80" fill="#F8FAFC" rx="15" stroke="#6B7280" stroke-width="2"/>
  
  <!-- 8根线的颜色 -->
  <rect x="250" y="340" width="150" height="40" fill="#FF6B35" rx="5"/>
  <text x="325" y="365" class="wire-text" fill="white">1.橙白</text>
  
  <rect x="420" y="340" width="150" height="40" fill="#FF8C00" rx="5"/>
  <text x="495" y="365" class="wire-text" fill="white">2.橙</text>
  
  <rect x="590" y="340" width="150" height="40" fill="#90EE90" rx="5"/>
  <text x="665" y="365" class="wire-text" fill="black">3.绿白</text>
  
  <rect x="760" y="340" width="150" height="40" fill="#0066CC" rx="5"/>
  <text x="835" y="365" class="wire-text" fill="white">4.蓝</text>
  
  <rect x="930" y="340" width="150" height="40" fill="#87CEEB" rx="5"/>
  <text x="1005" y="365" class="wire-text" fill="black">5.蓝白</text>
  
  <rect x="1100" y="340" width="150" height="40" fill="#32CD32" rx="5"/>
  <text x="1175" y="365" class="wire-text" fill="white">6.绿</text>
  
  <rect x="1270" y="340" width="150" height="40" fill="#D2B48C" rx="5"/>
  <text x="1345" y="365" class="wire-text" fill="black">7.棕白</text>
  
  <rect x="1440" y="340" width="150" height="40" fill="#8B4513" rx="5"/>
  <text x="1515" y="365" class="wire-text" fill="white">8.棕</text>
  
  <!-- 制作步骤 -->
  <rect x="100" y="460" width="1720" height="350" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="510" class="section-title" fill="#059669">制作步骤：</text>
  
  <!-- 步骤流程 -->
  <rect x="200" y="550" width="280" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="340" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">1️⃣ 剥线</text>
  <text x="220" y="620" class="step-text">剥除外皮约2cm</text>
  <text x="220" y="650" class="step-text">露出8根芯线</text>
  
  <path d="M 480 610 L 520 610" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <rect x="540" y="550" width="280" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="680" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">2️⃣ 排列</text>
  <text x="560" y="620" class="step-text">按T568B线序</text>
  <text x="560" y="650" class="step-text">整齐排列</text>
  
  <path d="M 820 610 L 860 610" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <rect x="880" y="550" width="280" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1020" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">3️⃣ 剪齐</text>
  <text x="900" y="620" class="step-text">剪成同等长度</text>
  <text x="900" y="650" class="step-text">约1.2-1.5cm</text>
  
  <path d="M 1160 610 L 1200 610" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <rect x="1220" y="550" width="280" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1360" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">4️⃣ 插入</text>
  <text x="1240" y="620" class="step-text">插入水晶头</text>
  <text x="1240" y="650" class="step-text">确保到底</text>
  
  <!-- 第二行步骤 -->
  <rect x="540" y="690" width="280" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="680" y="730" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">5️⃣ 压接</text>
  <text x="560" y="760" class="step-text">用力压接</text>
  <text x="560" y="790" class="step-text">确保牢固</text>
  
  <path d="M 820 750 L 860 750" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <rect x="880" y="690" width="280" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1020" y="730" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">6️⃣ 测试</text>
  <text x="900" y="760" class="step-text">用测线仪检查</text>
  <text x="900" y="790" class="step-text">8芯全通</text>
  
  <!-- 测试说明 -->
  <rect x="100" y="840" width="1720" height="120" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <text x="150" y="890" class="section-title" fill="#EA580C">测试：</text>
  <text x="150" y="930" class="content-text">用测线仪检查8芯是否全部导通且线序正确</text>
  <text x="150" y="960" class="step-text">✓ 8个灯全亮 ✓ 顺序正确 ✓ 无短路断路</text>
  
  <!-- 现场演示提示 -->
  <rect x="200" y="980" width="1520" height="80" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1010" class="demo-text">🎥 现场演示/视频：演示制作过程</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">熟能生巧，多练习才能做出标准的水晶头！</text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669"/>
    </marker>
  </defs>
</svg>
