<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .definition-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: normal; fill: #374151; }
      .score-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; text-anchor: middle; }
      .desc-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; }
      .goal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #059669; text-anchor: middle; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 38px; font-weight: bold; fill: #DC2626; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">口碑的"温度计"：NPS净推荐值</text>
  
  <!-- NPS定义 -->
  <rect x="100" y="130" width="1720" height="120" fill="#F8FAFC" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="170" class="definition-text" fill="#3B82F6">什么是NPS？</text>
  <text x="150" y="210" class="definition-text">简单说，就是问用户"你有多大可能把我们的宽带推荐给亲友？" (0-10分打分)</text>
  
  <!-- NPS分类区域 -->
  <!-- 推荐者 9-10分 -->
  <rect x="100" y="300" width="500" height="300" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="4"/>
  <text x="350" y="350" class="score-text" fill="#059669">9-10分：推荐者 👍</text>
  <text x="150" y="410" class="desc-text">他们是我们的"粉丝"，</text>
  <text x="150" y="450" class="desc-text">会主动说好话。</text>
  <text x="150" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #059669;">✓ 主动推荐</text>
  <text x="150" y="555" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #059669;">✓ 口碑传播</text>
  
  <!-- 被动者 7-8分 -->
  <rect x="710" y="300" width="500" height="300" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="4"/>
  <text x="960" y="350" class="score-text" fill="#F59E0B">7-8分：被动者 😐</text>
  <text x="760" y="410" class="desc-text">觉得还行，但没啥特别感觉，</text>
  <text x="760" y="450" class="desc-text">容易被对手挖走。</text>
  <text x="760" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #F59E0B;">⚠ 中性态度</text>
  <text x="760" y="555" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #F59E0B;">⚠ 易流失</text>
  
  <!-- 贬损者 0-6分 -->
  <rect x="1320" y="300" width="500" height="300" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="4"/>
  <text x="1570" y="350" class="score-text" fill="#DC2626">0-6分：贬损者 👎</text>
  <text x="1370" y="410" class="desc-text">不满意！不仅自己可能要走，</text>
  <text x="1370" y="450" class="desc-text">还会吐槽劝退别人！</text>
  <text x="1370" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #DC2626;">✗ 负面传播</text>
  <text x="1370" y="555" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #DC2626;">✗ 主动流失</text>
  
  <!-- 目标区域 -->
  <rect x="200" y="650" width="1520" height="150" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="960" y="700" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">我们的目标：</text>
  <text x="960" y="750" class="goal-text">争取更多推荐者，减少贬损者！</text>
  
  <!-- 关键联系 -->
  <rect x="300" y="850" width="1320" height="120" fill="#FEFCE8" rx="20" stroke="#EAB308" stroke-width="3"/>
  <text x="960" y="900" class="key-text" fill="#EAB308">关键联系：</text>
  <text x="960" y="950" class="key-text" fill="#DC2626">你的服务质量，直接决定用户打几分！</text>
  
  <!-- 装饰元素 -->
  <circle cx="350" cy="250" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="960" cy="250" r="30" fill="#F59E0B" opacity="0.3"/>
  <circle cx="1570" cy="250" r="30" fill="#DC2626" opacity="0.3"/>
</svg>
