<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .content-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .target-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; }
      .recommend-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">平台话术库：你的"销售宝典"</text>
  <text x="960" y="140" class="subtitle-text">随销助手话术库 (回顾与强调)</text>
  
  <!-- 话术库内容 -->
  <rect x="100" y="180" width="1720" height="650" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">内容：(再次展示针对不同场景和产品的标准推荐话术)</text>
  
  <!-- 游戏用户 -->
  <rect x="200" y="280" width="700" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="320" class="content-title" fill="#3B82F6">针对游戏用户</text>
  <text x="220" y="350" class="target-text">→ 推荐电竞路由/专线</text>
  <text x="220" y="375" class="recommend-text">"您经常玩游戏吧？我们有专门的电竞套餐，延迟更低，游戏体验更好…"</text>
  
  <!-- 老人小孩家庭 -->
  <rect x="950" y="280" width="700" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="970" y="320" class="content-title" fill="#059669">针对家有老人/小孩</text>
  <text x="970" y="350" class="target-text">→ 推荐安防/云存储</text>
  <text x="970" y="375" class="recommend-text">"家里有老人孩子，安全很重要。我们的智能摄像头可以随时查看…"</text>
  
  <!-- 大户型信号差 -->
  <rect x="200" y="420" width="700" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="460" class="content-title" fill="#EA580C">针对大户型/信号差</text>
  <text x="220" y="490" class="target-text">→ 推荐Mesh组网</text>
  <text x="220" y="515" class="recommend-text">"您家面积挺大的，单个路由器可能覆盖不全。Mesh组网能保证全屋信号…"</text>
  
  <!-- 手机存储满 -->
  <rect x="950" y="420" width="700" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="970" y="460" class="content-title" fill="#7C3AED">针对手机存储满</text>
  <text x="970" y="490" class="target-text">→ 推荐家庭云</text>
  <text x="970" y="515" class="recommend-text">"手机照片太多了吧？家庭云能自动备份，还能全家共享…"</text>
  
  <!-- 使用场景 -->
  <rect x="200" y="560" width="1520" height="120" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <text x="960" y="600" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">使用场景</text>
  <text x="220" y="630" class="target-text">现场观察用户需求 → 平台查询推荐话术 → 结合实际情况灵活应用</text>
  <text x="220" y="655" class="recommend-text">话术是工具，沟通是艺术，成功靠技巧</text>
  
  <!-- 关键强调 -->
  <rect x="200" y="700" width="1520" height="100" fill="#E0F2FE" rx="15"/>
  <text x="960" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #0284C7; text-anchor: middle;">[关键]：</text>
  <text x="960" y="770" class="key-text" fill="#1E3A8A">平台提供的是"弹药"，怎么"打"得准，还需要结合你的沟通技巧</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="860" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="910" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">核心理念</text>
  <text x="960" y="950" class="key-text" fill="#1E3A8A">工具+技巧=成功</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让平台成为你的得力助手，让话术成为你的制胜法宝</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="400" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="400" r="30" fill="#059669" opacity="0.3"/>
</svg>
