<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .stage-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #374151; text-anchor: middle; }
      .channel-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #6B7280; text-anchor: middle; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #DC2626; text-anchor: middle; }
      .key-point { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; }
      .emphasis-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">服务触点无处不在</text>
  
  <!-- 用户旅程主线 -->
  <rect x="100" y="150" width="1720" height="200" fill="#F8FAFC" rx="20" stroke="#3B82F6" stroke-width="3"/>
  
  <!-- 旅程阶段 -->
  <!-- 咨询/办理 -->
  <rect x="150" y="180" width="200" height="140" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="250" y="220" class="stage-text" fill="#3B82F6">咨询/办理</text>
  <text x="250" y="250" class="channel-text">(营业厅/线上)</text>
  <circle cx="250" cy="300" r="25" fill="#3B82F6"/>
  <text x="250" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">1</text>
  
  <!-- 箭头1 -->
  <path d="M 350 250 L 420 250" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 上门安装 -->
  <rect x="450" y="180" width="200" height="140" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="550" y="220" class="stage-text" fill="#EA580C">上门安装</text>
  <text x="550" y="250" class="highlight-text">【关键触点】</text>
  <circle cx="550" cy="300" r="25" fill="#EA580C"/>
  <text x="550" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">2</text>
  
  <!-- 箭头2 -->
  <path d="M 650 250 L 720 250" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 日常使用 -->
  <rect x="750" y="180" width="200" height="140" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="850" y="210" class="stage-text" fill="#059669">日常使用</text>
  <text x="850" y="240" class="channel-text">(遇到问题求助)</text>
  <text x="850" y="270" class="highlight-text">【关键触点】</text>
  <circle cx="850" cy="300" r="25" fill="#059669"/>
  <text x="850" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">3</text>
  
  <!-- 箭头3 -->
  <path d="M 950 250 L 1020 250" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 续约/升级 -->
  <rect x="1050" y="180" width="200" height="140" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="1150" y="220" class="stage-text" fill="#7C3AED">续约/升级咨询</text>
  <circle cx="1150" cy="300" r="25" fill="#7C3AED"/>
  <text x="1150" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">4</text>
  
  <!-- 箭头4 -->
  <path d="M 1250 250 L 1320 250" fill="none" stroke="#3B82F6" stroke-width="6" marker-end="url(#arrowhead)"/>
  
  <!-- 流失 -->
  <rect x="1350" y="180" width="200" height="140" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1450" y="220" class="stage-text" fill="#DC2626">(希望不是)流失</text>
  <circle cx="1450" cy="300" r="25" fill="#DC2626"/>
  <text x="1450" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">5</text>
  
  <!-- 关键触点说明 -->
  <rect x="200" y="400" width="1520" height="300" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="450" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">关键触点</text>
  
  <text x="250" y="520" class="key-point">每次与用户的互动，特别是：</text>
  <text x="300" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151;">• 上门服务 - 面对面接触，直观感受服务专业度</text>
  <text x="300" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151;">• 电话沟通 - 声音传递态度，话术体现专业</text>
  <text x="300" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151;">都是影响用户感知的关键时刻！</text>
  
  <!-- 底部强调 -->
  <rect x="300" y="750" width="1320" height="120" fill="#EFF6FF" rx="20"/>
  <text x="960" y="800" class="emphasis-text">一线人员是用户感知移动服务质量</text>
  <text x="960" y="850" class="emphasis-text">最直接的窗口</text>
  
  <!-- 装饰元素 -->
  <circle cx="150" cy="500" r="40" fill="#F59E0B" opacity="0.2"/>
  <circle cx="1770" cy="500" r="40" fill="#F59E0B" opacity="0.2"/>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6"/>
    </marker>
  </defs>
</svg>
