<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .step-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #374151; font-style: italic; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">化解抱怨的"钥匙"：共情+解决</text>
  <text x="960" y="140" class="subtitle-text">投诉场景应对心法：先情后理</text>
  
  <!-- 四步法流程 -->
  <rect x="100" y="180" width="1720" height="700" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">四步法：化解投诉的标准流程</text>
  
  <!-- 第一步：倾听与共情 -->
  <rect x="200" y="280" width="700" height="180" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <circle cx="280" cy="340" r="30" fill="#3B82F6"/>
  <text x="280" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">1</text>
  <text x="550" y="330" class="step-title" fill="#3B82F6">倾听与共情</text>
  <text x="550" y="360" class="key-text" fill="#DC2626">(处理心情)</text>
  <text x="320" y="390" class="speech-text">"嗯嗯，我听明白了…"</text>
  <text x="320" y="415" class="speech-text">"您先别着急，我理解您的感受…"</text>
  <text x="320" y="440" class="step-desc">让用户把话说完，表示理解他的情绪</text>
  
  <!-- 第二步：核实与分析 -->
  <rect x="950" y="280" width="700" height="180" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <circle cx="1030" cy="340" r="30" fill="#059669"/>
  <text x="1030" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">2</text>
  <text x="1300" y="330" class="step-title" fill="#059669">核实与分析</text>
  <text x="1300" y="360" class="key-text" fill="#DC2626">(找到问题)</text>
  <text x="1070" y="390" class="speech-text">"为了更好地帮您解决，我需要</text>
  <text x="1070" y="415" class="speech-text">先了解一下具体情况…"</text>
  <text x="1070" y="440" class="step-desc">通过询问、检测找到问题的症结</text>
  
  <!-- 第三步：方案与行动 -->
  <rect x="200" y="480" width="700" height="180" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <circle cx="280" cy="540" r="30" fill="#EA580C"/>
  <text x="280" y="550" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">3</text>
  <text x="550" y="530" class="step-title" fill="#EA580C">方案与行动</text>
  <text x="550" y="560" class="key-text" fill="#DC2626">(解决问题)</text>
  <text x="320" y="590" class="speech-text">"根据我的判断，可能是[原因]，</text>
  <text x="320" y="615" class="speech-text">我们可以试试[解决方案]…"</text>
  <text x="320" y="640" class="step-desc">给出具体的、可操作的解决方案</text>
  
  <!-- 第四步：确认与跟进 -->
  <rect x="950" y="480" width="700" height="180" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <circle cx="1030" cy="540" r="30" fill="#7C3AED"/>
  <text x="1030" y="550" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: white; text-anchor: middle;">4</text>
  <text x="1300" y="530" class="step-title" fill="#7C3AED">确认与跟进</text>
  <text x="1300" y="560" class="key-text" fill="#DC2626">(追踪效果)</text>
  <text x="1070" y="590" class="speech-text">"您看现在是不是好多了？"</text>
  <text x="1070" y="615" class="speech-text">"如果还有问题，您可以随时…"</text>
  <text x="1070" y="640" class="step-desc">确认效果，留下后续联系方式</text>
  
  <!-- 流程箭头 -->
  <path d="M 900 370 L 950 370" fill="none" stroke="#6B7280" stroke-width="6" marker-end="url(#arrowhead)"/>
  <path d="M 1300 460 L 1300 480" fill="none" stroke="#6B7280" stroke-width="6" marker-end="url(#arrowdown)"/>
  <path d="M 950 570 L 900 570" fill="none" stroke="#6B7280" stroke-width="6" marker-end="url(#arrowleft)"/>
  
  <!-- 核心要点 -->
  <rect x="300" y="700" width="1320" height="150" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 核心要点</text>
  <text x="960" y="780" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">先处理心情，再处理事情</text>
  <text x="960" y="820" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">用户生气时，讲道理没用；用户冷静后，解决问题才有效！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="580" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="580" r="25" fill="#7C3AED" opacity="0.3"/>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280"/>
    </marker>
    <marker id="arrowdown" markerWidth="10" markerHeight="7" refX="5" refY="6" orient="auto">
      <polygon points="0 0, 10 0, 5 7" fill="#6B7280"/>
    </marker>
    <marker id="arrowleft" markerWidth="10" markerHeight="7" refX="1" refY="3.5" orient="auto">
      <polygon points="10 0, 0 3.5, 10 7" fill="#6B7280"/>
    </marker>
  </defs>
</svg>
