<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .benefit-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">做活动？发海报？平台帮你搞定！</text>
  <text x="960" y="140" class="subtitle-text">平台核心功能3：营销素材，一键转发</text>
  
  <!-- 功能界面 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="section-title" fill="#3B82F6">功能界面：</text>
  <text x="150" y="260" class="step-text">[截图：素材库入口及海报/文案模板列表]</text>
  
  <!-- 使用步骤 -->
  <rect x="100" y="310" width="1720" height="500" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="150" y="360" class="section-title" fill="#1E3A8A">怎么用？</text>
  
  <!-- 查找素材 -->
  <rect x="200" y="400" width="1520" height="100" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="430" class="step-title" fill="#3B82F6">查找素材：</text>
  <text x="220" y="460" class="step-text">根据当前进行的营销活动（如节日促销、新品上市）或推荐场景，</text>
  <text x="220" y="485" class="step-text">在素材库找到对应的海报、朋友圈文案、短信模板</text>
  
  <!-- 个性化修改 -->
  <rect x="200" y="520" width="1520" height="80" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="550" class="step-title" fill="#059669">个性化修改（若支持）：</text>
  <text x="220" y="575" class="step-text">可简单修改联系方式等信息</text>
  
  <!-- 一键分享 -->
  <rect x="200" y="620" width="1520" height="100" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="650" class="step-title" fill="#EA580C">一键分享：</text>
  <text x="220" y="680" class="step-text">直接将选好的素材分享到微信好友、朋友圈，或复制文案/链接</text>
  <text x="220" y="705" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">操作简单，效率高</text>
  
  <!-- 好处 -->
  <rect x="200" y="740" width="1520" height="50" fill="#F3E8FF" rx="15"/>
  <text x="220" y="770" class="step-title" fill="#7C3AED">好处：</text>
  <text x="350" y="770" class="step-text">素材专业、信息准确、操作便捷，还能带上你的专属推广码(若有)</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="840" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="890" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">核心价值</text>
  <text x="960" y="930" class="benefit-text" fill="#1E3A8A">专业素材 + 便捷分享 = 高效营销</text>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让每个人都能成为营销达人</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
