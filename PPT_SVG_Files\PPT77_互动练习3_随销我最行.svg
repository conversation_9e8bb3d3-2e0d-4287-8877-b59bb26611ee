<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .exercise-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .practice-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .instruction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">我是"金牌"维系能手</text>
  <text x="960" y="140" class="subtitle-text">互动练习3：随销我最行</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">🎯</text>
  
  <!-- 练习说明 -->
  <rect x="100" y="300" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="340" class="exercise-title" fill="#3B82F6">练习说明：</text>
  <text x="150" y="380" class="instruction-text">模拟上门服务场景，练习推荐升档/组网/家庭云的话术</text>
  
  <!-- 模拟场景 -->
  <rect x="100" y="430" width="1720" height="500" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="960" y="480" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">模拟场景</text>
  
  <!-- 场景1：升档推荐 -->
  <rect x="200" y="520" width="1520" height="100" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="550" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3B82F6;">场景1：宽带升档推荐</text>
  <text x="220" y="580" class="scenario-text">用户抱怨："师傅，我家网速有点慢，看4K电视经常卡顿，能解决吗？"</text>
  <text x="220" y="605" class="practice-text">练习要点：运用黄金三问话术，推荐千兆宽带升级</text>
  
  <!-- 场景2：Mesh组网 -->
  <rect x="200" y="640" width="1520" height="100" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="670" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">场景2：Mesh组网推荐</text>
  <text x="220" y="700" class="scenario-text">用户反映："主卧WiFi信号很差，经常断线，孩子上网课都受影响。"</text>
  <text x="220" y="725" class="practice-text">练习要点：分析户型特点，推荐Mesh智能组网解决方案</text>
  
  <!-- 场景3：家庭云 -->
  <rect x="200" y="760" width="1520" height="100" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="790" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">场景3：家庭云推荐</text>
  <text x="220" y="820" class="scenario-text">用户苦恼："手机照片太多了，存储空间不够，又舍不得删除孩子的照片。"</text>
  <text x="220" y="845" class="practice-text">练习要点：突出家庭云的安全备份和共享价值</text>
  
  <!-- 演练要求 -->
  <rect x="200" y="880" width="1520" height="40" fill="#FFFBEB" rx="15"/>
  <text x="220" y="905" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #F59E0B;">演练要求：两人一组，一人扮演用户，一人扮演装维师傅，现场模拟推荐过程</text>
  
  <!-- 评分标准 -->
  <rect x="200" y="950" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">评分标准</text>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">需求挖掘 + 方案匹配 + 话术自然 + 价值突出 + 促成技巧</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280; text-anchor: middle;">重点考察现场应变能力和专业推荐技巧</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="700" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="700" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
