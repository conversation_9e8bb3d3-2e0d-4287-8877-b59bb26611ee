<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .function-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .function-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .feature-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">爱家APP：你的随身WiFi助手</text>
  <text x="960" y="140" class="subtitle-text">工具实战4：爱家APP WiFi检测</text>
  
  <!-- 功能介绍 -->
  <rect x="100" y="180" width="1720" height="700" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">核心功能展示</text>
  
  <!-- 测速功能 -->
  <rect x="200" y="280" width="700" height="180" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="550" y="320" class="icon-text">📶</text>
  <text x="550" y="370" class="function-title" fill="#3B82F6">测速</text>
  <text x="220" y="410" class="function-desc">"一键测速"功能，快速了解当前网速</text>
  <text x="220" y="440" class="feature-text">• 下载/上传速率测试</text>
  
  <!-- 信号强度检测 -->
  <rect x="950" y="280" width="700" height="180" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="1300" y="320" class="icon-text">📡</text>
  <text x="1300" y="370" class="function-title" fill="#059669">信号强度检测</text>
  <text x="970" y="410" class="function-desc">查看当前连接的WiFi信号强度（dBm值）</text>
  <text x="970" y="440" class="feature-text">• 实时信号监测</text>
  
  <!-- 信道分析 -->
  <rect x="200" y="480" width="700" height="180" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="550" y="520" class="icon-text">📊</text>
  <text x="550" y="570" class="function-title" fill="#EA580C">信道分析</text>
  <text x="220" y="610" class="function-desc">查看周围WiFi信道占用情况</text>
  <text x="220" y="640" class="feature-text">• 辅助判断干扰源</text>
  
  <!-- 设备连接列表 -->
  <rect x="950" y="480" width="700" height="180" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="1300" y="520" class="icon-text">📱</text>
  <text x="1300" y="570" class="function-title" fill="#7C3AED">设备连接列表</text>
  <text x="970" y="610" class="function-desc">查看当前有多少设备连接了WiFi</text>
  <text x="970" y="640" class="feature-text">• 设备管理功能</text>
  
  <!-- APP界面示意 -->
  <rect x="300" y="700" width="1320" height="150" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">📱 APP界面示意</text>
  
  <!-- 模拟手机界面 -->
  <rect x="400" y="770" width="200" height="60" fill="white" rx="10" stroke="#3B82F6" stroke-width="2"/>
  <text x="500" y="805" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">测速：285M ↓</text>
  
  <rect x="650" y="770" width="200" height="60" fill="white" rx="10" stroke="#059669" stroke-width="2"/>
  <text x="750" y="805" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">信号：-25dBm</text>
  
  <rect x="900" y="770" width="200" height="60" fill="white" rx="10" stroke="#EA580C" stroke-width="2"/>
  <text x="1000" y="805" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">信道：6 (空闲)</text>
  
  <rect x="1150" y="770" width="200" height="60" fill="white" rx="10" stroke="#7C3AED" stroke-width="2"/>
  <text x="1250" y="805" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: normal; fill: #374151; text-anchor: middle;">设备：8台</text>
  
  <!-- 使用优势 -->
  <rect x="200" y="880" width="1520" height="120" fill="#F0FDF4" rx="20"/>
  <text x="960" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">💡 使用优势</text>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">统一工具，标准化检测，数据准确可靠，用户信任度高！</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">一个APP搞定所有WiFi检测需求</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="400" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="400" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="600" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="600" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
