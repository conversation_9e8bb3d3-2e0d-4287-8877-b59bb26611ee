<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">不会开口？平台给你"递话筒"！</text>
  <text x="960" y="140" class="subtitle-text">平台核心功能2：随销助手，精准推荐</text>
  
  <!-- 功能界面 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="section-title" fill="#3B82F6">功能界面：</text>
  <text x="150" y="260" class="step-text">[截图：随销助手入口及推荐界面]</text>
  
  <!-- 使用步骤 -->
  <rect x="100" y="310" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="150" y="360" class="section-title" fill="#1E3A8A">怎么用？</text>
  
  <!-- 触发场景 -->
  <rect x="200" y="400" width="1520" height="100" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="430" class="step-title" fill="#3B82F6">触发场景：</text>
  <text x="220" y="460" class="step-text">上门服务中，发现用户有潜在需求（如抱怨信号差、咨询提速）</text>
  <text x="220" y="485" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">敏锐捕捉用户需求信号</text>
  
  <!-- 查询用户 -->
  <rect x="200" y="520" width="1520" height="100" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="550" class="step-title" fill="#059669">查询用户：</text>
  <text x="220" y="580" class="step-text">在助手中输入用户号码或标签 (系统也可能自动推送)</text>
  <text x="220" y="605" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">快速获取用户画像信息</text>
  
  <!-- 查看推荐 -->
  <rect x="200" y="640" width="1520" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="670" class="step-title" fill="#EA580C">查看推荐：</text>
  <text x="220" y="700" class="step-text">平台会根据用户信息，推荐合适的增值业务（如Mesh、家庭云、升档套餐）</text>
  <text x="220" y="730" class="step-text">和参考话术</text>
  <text x="220" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">智能匹配，精准推荐</text>
  
  <!-- 灵活应用 -->
  <rect x="200" y="780" width="1520" height="100" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="220" y="810" class="step-title" fill="#7C3AED">灵活应用：</text>
  <text x="220" y="840" class="step-text">将推荐话术结合现场沟通情况，自然地向用户介绍</text>
  <text x="220" y="865" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">话术是参考，沟通靠技巧</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="930" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">核心价值</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">让每一次服务都成为精准营销的机会</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">科技赋能，智能推荐，提升成功率</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
</svg>
