<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 72px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: normal; fill: #3B82F6; text-anchor: middle; }
      .prompt-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: normal; fill: #374151; }
      .example-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 120px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 0 200 Q 480 100 960 200 T 1920 200" fill="none" stroke="#3B82F6" stroke-width="6" opacity="0.3"/>
  <path d="M 0 880 Q 480 980 960 880 T 1920 880" fill="none" stroke="#3B82F6" stroke-width="6" opacity="0.3"/>
  
  <!-- 主图标 -->
  <text x="960" y="350" class="icon-text">🔧</text>
  
  <!-- 标题 -->
  <text x="960" y="450" class="title-text">Q&amp;A 互动交流</text>
  
  <!-- 副标题 -->
  <text x="960" y="520" class="subtitle-text">解答关于入网质量管控的技术疑问</text>
  
  <!-- 提示区域 -->
  <rect x="200" y="580" width="1520" height="300" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="3"/>
  
  <text x="250" y="640" class="prompt-text" fill="#3B82F6">🙋‍♂️ 欢迎提问：</text>
  <text x="280" y="690" class="example-text">• 光衰值测试中遇到的具体问题？</text>
  <text x="280" y="730" class="example-text">• WiFi测速不达标时如何快速排查？</text>
  <text x="280" y="770" class="example-text">• 熔接操作中的技巧和注意事项？</text>
  <text x="280" y="810" class="example-text">• AI质检小程序使用中的困惑？</text>
  <text x="280" y="850" class="example-text">• 用户验收时的沟通技巧？</text>
  
  <!-- 鼓励参与 -->
  <rect x="400" y="920" width="1120" height="80" fill="#F0FDF4" rx="15"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #059669; text-anchor: middle;">实践出真知，交流促提升！</text>
  
  <!-- 装饰元素 -->
  <circle cx="150" cy="400" r="50" fill="#E0F2FE" opacity="0.6"/>
  <circle cx="1770" cy="400" r="50" fill="#DBEAFE" opacity="0.6"/>
  <circle cx="150" cy="700" r="40" fill="#F0F9FF" opacity="0.5"/>
  <circle cx="1770" cy="700" r="40" fill="#EFF6FF" opacity="0.5"/>
  
  <!-- 技术图标装饰 -->
  <text x="150" y="420" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">📊</text>
  <text x="1770" y="420" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">🔍</text>
</svg>
