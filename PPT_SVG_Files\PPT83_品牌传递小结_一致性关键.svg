<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .point-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .point-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .detail-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">让用户处处感受到"爱家"</text>
  <text x="960" y="140" class="subtitle-text">品牌传递小结：一致性是关键</text>
  
  <!-- 要点展示 -->
  <rect x="100" y="180" width="1720" height="650" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">要点：</text>
  
  <!-- 统一的语言 -->
  <rect x="200" y="280" width="480" height="180" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="440" y="320" class="point-title" fill="#3B82F6">统一的语言</text>
  <text x="220" y="360" class="point-text">(标准话术)</text>
  <text x="220" y="390" class="detail-text">• 开场白统一</text>
  <text x="220" y="420" class="detail-text">• 品牌名称统一</text>
  <text x="220" y="450" class="detail-text">• 结束语统一</text>
  
  <!-- 统一的形象 -->
  <rect x="720" y="280" width="480" height="180" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="960" y="320" class="point-title" fill="#059669">统一的形象</text>
  <text x="740" y="360" class="point-text">(工服工牌、工具物料)</text>
  <text x="740" y="390" class="detail-text">• 着装规范</text>
  <text x="740" y="420" class="detail-text">• 工具整洁</text>
  <text x="740" y="450" class="detail-text">• 物料统一</text>
  
  <!-- 统一的动作 -->
  <rect x="1240" y="280" width="480" height="180" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="1480" y="320" class="point-title" fill="#EA580C">统一的动作</text>
  <text x="1260" y="360" class="point-text">(服务流程标准化)</text>
  <text x="1260" y="390" class="detail-text">• 服务流程</text>
  <text x="1260" y="420" class="detail-text">• 操作规范</text>
  <text x="1260" y="450" class="detail-text">• 品质标准</text>
  
  <!-- 一致性价值 -->
  <rect x="200" y="480" width="1520" height="150" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="960" y="520" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">一致性的价值</text>
  <text x="220" y="560" class="point-text">建立用户信任 → 强化品牌认知 → 提升服务体验 → 形成口碑传播</text>
  <text x="220" y="590" class="detail-text">每一次一致的体验都在为品牌加分</text>
  <text x="220" y="615" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #7C3AED;">让用户无论何时何地接触移动爱家，都有相同的优质感受</text>
  
  <!-- 执行要求 -->
  <rect x="200" y="650" width="1520" height="100" fill="#FFFBEB" rx="15"/>
  <text x="960" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">执行要求</text>
  <text x="220" y="720" class="point-text">人人都是品牌大使，事事体现爱家标准</text>
  <text x="220" y="745" class="detail-text">从细节做起，从自己做起，让品牌价值在每一次服务中得到体现</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="860" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="910" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">核心理念</text>
  <text x="960" y="950" class="core-text" fill="#1E3A8A">一致性塑造品牌，专业性赢得信任</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让移动爱家成为用户心中的优质服务代名词</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="700" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="700" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
