<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .exercise-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .scenario-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .type-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; text-anchor: middle; }
      .instruction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">我是"金牌"维系能手</text>
  <text x="960" y="140" class="subtitle-text">互动练习2：话术对对碰</text>
  
  <!-- 练习说明 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="exercise-title" fill="#3B82F6">练习说明：</text>
  <text x="150" y="260" class="instruction-text">给出用户抱怨场景，让学员匹配最合适的挽留话术类型</text>
  
  <!-- 场景区域 -->
  <rect x="100" y="310" width="850" height="600" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="3"/>
  <text x="525" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">用户抱怨场景</text>
  
  <!-- 场景1 -->
  <rect x="150" y="380" width="750" height="80" fill="#FEF2F2" rx="10" stroke="#DC2626" stroke-width="2"/>
  <text x="525" y="410" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #DC2626; text-anchor: middle;">场景A</text>
  <text x="170" y="440" class="scenario-text">"你们移动太贵了！隔壁联通便宜多了，我要换！"</text>
  
  <!-- 场景2 -->
  <rect x="150" y="480" width="750" height="80" fill="#FEF7ED" rx="10" stroke="#EA580C" stroke-width="2"/>
  <text x="525" y="510" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C; text-anchor: middle;">场景B</text>
  <text x="170" y="540" class="scenario-text">"网络老是断，师傅态度还不好，我受够了！"</text>
  
  <!-- 场景3 -->
  <rect x="150" y="580" width="750" height="80" fill="#F0FDF4" rx="10" stroke="#059669" stroke-width="2"/>
  <text x="525" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">场景C</text>
  <text x="170" y="640" class="scenario-text">"我就是个老人，用不了这么多功能，太复杂了！"</text>
  
  <!-- 场景4 -->
  <rect x="150" y="680" width="750" height="80" fill="#F3E8FF" rx="10" stroke="#7C3AED" stroke-width="2"/>
  <text x="525" y="710" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">场景D</text>
  <text x="170" y="740" class="scenario-text">"合约还没到期就催我续约，是不是想多收钱？"</text>
  
  <!-- 话术类型区域 -->
  <rect x="970" y="310" width="850" height="600" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="3"/>
  <text x="1395" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">挽留话术类型</text>
  
  <!-- 类型1 -->
  <rect x="1020" y="380" width="750" height="80" fill="#E0F2FE" rx="10" stroke="#0284C7" stroke-width="2"/>
  <text x="1395" y="410" class="type-text" fill="#0284C7">① 价格敏感型话术</text>
  <text x="1040" y="440" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #4B5563;">算价值账，强调性价比和优惠</text>
  
  <!-- 类型2 -->
  <rect x="1020" y="480" width="750" height="80" fill="#ECFDF5" rx="10" stroke="#10B981" stroke-width="2"/>
  <text x="1395" y="510" class="type-text" fill="#10B981">② 服务不满型话术</text>
  <text x="1040" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #4B5563;">道歉+解决+补偿+跟进</text>
  
  <!-- 类型3 -->
  <rect x="1020" y="580" width="750" height="80" fill="#FEF3C7" rx="10" stroke="#F59E0B" stroke-width="2"/>
  <text x="1395" y="610" class="type-text" fill="#F59E0B">③ 需求引导型话术</text>
  <text x="1040" y="640" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #4B5563;">了解真实需求，推荐合适方案</text>
  
  <!-- 类型4 -->
  <rect x="1020" y="680" width="750" height="80" fill="#EDE9FE" rx="10" stroke="#8B5CF6" stroke-width="2"/>
  <text x="1395" y="710" class="type-text" fill="#8B5CF6">④ 信任建立型话术</text>
  <text x="1040" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #4B5563;">解释动机，建立信任关系</text>
  
  <!-- 练习要求 -->
  <rect x="200" y="940" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">练习要求</text>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">将左侧场景与右侧最合适的话术类型进行匹配，并说明理由</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280; text-anchor: middle;">参考答案：A-①  B-②  C-③  D-④</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="610" r="30" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="610" r="30" fill="#6B7280" opacity="0.3"/>
</svg>
