<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .item-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .form-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .form-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #6B7280; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">主动告知，贴心周到</text>
  <text x="960" y="140" class="subtitle-text">"一份指引"：授人以渔，减少求助</text>
  
  <!-- 核心内容 -->
  <rect x="100" y="180" width="1720" height="400" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">核心内容：</text>
  
  <!-- 内容项目 -->
  <rect x="200" y="270" width="750" height="120" fill="#DBEAFE" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3B82F6;">📶 WiFi信息</text>
  <text x="220" y="340" class="item-text">• WiFi名称(SSID)和密码</text>
  <text x="220" y="370" class="item-text">• 初始或修改后的密码都要告知</text>
  
  <rect x="970" y="270" width="750" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="990" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">💡 指示灯含义</text>
  <text x="990" y="340" class="item-text">• 电源/网络/WiFi灯状态说明</text>
  <text x="990" y="370" class="item-text">• 正常/异常状态的区别</text>
  
  <rect x="200" y="410" width="750" height="120" fill="#FED7AA" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="450" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">📱 爱家APP使用</text>
  <text x="220" y="480" class="item-text">• WiFi管理功能介绍</text>
  <text x="220" y="510" class="item-text">• 故障报修操作方法</text>
  
  <rect x="970" y="410" width="750" height="120" fill="#E0E7FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="990" y="450" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED;">🔧 自助排障</text>
  <text x="990" y="480" class="item-text">• 重启光猫/路由器方法</text>
  <text x="990" y="510" class="item-text">• 简单问题自己解决</text>
  
  <!-- 形式 -->
  <rect x="100" y="610" width="1720" height="300" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="660" class="section-title" fill="#059669">形式：</text>
  
  <!-- 基础形式 -->
  <rect x="200" y="700" width="480" height="180" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="440" y="740" class="form-title" fill="#059669">基础必做</text>
  <text x="220" y="780" class="form-text">✓ 口头讲解</text>
  <text x="220" y="810" class="form-text">✓ APP演示</text>
  <text x="220" y="840" class="form-text">✓ 现场指导操作</text>
  
  <!-- 配合提供 -->
  <rect x="720" y="700" width="480" height="180" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="960" y="740" class="form-title" fill="#059669">配合提供</text>
  <text x="740" y="780" class="form-text">✓ 标准化《用户服务指引卡》</text>
  <text x="740" y="810" class="form-text">✓ WiFi密码卡片</text>
  <text x="740" y="840" class="form-text">✓ 联系方式卡片</text>
  
  <!-- 引导关注 -->
  <rect x="1240" y="700" width="480" height="180" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1480" y="740" class="form-title" fill="#059669">引导关注</text>
  <text x="1260" y="780" class="form-text">✓ 扫码关注公众号</text>
  <text x="1260" y="810" class="form-text">✓ 获取电子版帮助</text>
  <text x="1260" y="840" class="form-text">✓ 在线自助服务</text>
  
  <!-- 效果说明 -->
  <rect x="200" y="940" width="1520" height="100" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 效果</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">用户会用了，求助电话就少了；我们省心，用户也更满意！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="750" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="750" r="25" fill="#059669" opacity="0.3"/>
</svg>
