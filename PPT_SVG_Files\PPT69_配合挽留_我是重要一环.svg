<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
      .action-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">用户想"走"？尽力拉一把！</text>
  <text x="960" y="140" class="subtitle-text">配合挽留：我是重要一环</text>
  
  <!-- 问题引入 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="section-title" fill="#3B82F6">接到挽留任务/发现高风险用户后，我该做什么？</text>
  
  <!-- 五步行动 -->
  <rect x="100" y="310" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="960" y="360" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">五步挽留行动</text>
  
  <!-- 第1步：快速响应 -->
  <rect x="200" y="400" width="1520" height="80" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <circle cx="280" cy="440" r="25" fill="#3B82F6"/>
  <text x="280" y="450" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">1</text>
  <text x="330" y="430" class="step-title" fill="#3B82F6">快速响应</text>
  <text x="330" y="460" class="action-text">尽快联系用户（若任务指派）</text>
  
  <!-- 第2步：倾听原因 -->
  <rect x="200" y="500" width="1520" height="80" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <circle cx="280" cy="540" r="25" fill="#059669"/>
  <text x="280" y="550" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">2</text>
  <text x="330" y="530" class="step-title" fill="#059669">倾听原因</text>
  <text x="330" y="560" class="action-text">耐心了解用户不满的具体原因是什么？（是网络问题？服务问题？还是价格问题？）</text>
  
  <!-- 第3步：应用策略 -->
  <rect x="200" y="600" width="1520" height="80" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <circle cx="280" cy="640" r="25" fill="#EA580C"/>
  <text x="280" y="650" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">3</text>
  <text x="330" y="630" class="step-title" fill="#EA580C">应用策略</text>
  <text x="330" y="660" class="action-text">根据用户类型和原因，尝试使用对应的挽留话术和安抚措施（调用话术库）</text>
  
  <!-- 第4步：传递关怀 -->
  <rect x="200" y="700" width="1520" height="80" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <circle cx="280" cy="740" r="25" fill="#7C3AED"/>
  <text x="280" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">4</text>
  <text x="330" y="730" class="step-title" fill="#7C3AED">传递关怀</text>
  <text x="330" y="760" class="action-text">表达对用户的重视，即使无法立即解决，也要表明会跟进处理</text>
  
  <!-- 第5步：如实反馈 -->
  <rect x="200" y="800" width="1520" height="80" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <circle cx="280" cy="840" r="25" fill="#F59E0B"/>
  <text x="280" y="850" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">5</text>
  <text x="330" y="830" class="step-title" fill="#F59E0B">如实反馈</text>
  <text x="330" y="860" class="action-text">将沟通情况、用户态度、处理结果及时反馈给系统或上级</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="940" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">核心理念</text>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">真诚沟通，用心服务，每一次挽留都是挽回用户信任的机会</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">我们是用户与公司之间的重要桥梁</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="640" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="640" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
