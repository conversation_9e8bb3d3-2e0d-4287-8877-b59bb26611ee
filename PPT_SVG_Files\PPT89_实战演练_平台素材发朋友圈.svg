<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .step-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .encourage-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">动动手指，轻松营销</text>
  <text x="960" y="140" class="subtitle-text">实战演练：用平台素材发个朋友圈</text>
  
  <!-- 步骤演示 -->
  <rect x="100" y="180" width="1720" height="650" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">步骤演示：</text>
  
  <!-- 步骤a -->
  <rect x="200" y="280" width="1520" height="80" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <circle cx="280" cy="320" r="25" fill="#3B82F6"/>
  <text x="280" y="330" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">a</text>
  <text x="330" y="310" class="step-title" fill="#3B82F6">打开爱家APP，进入"素材库"</text>
  <text x="330" y="340" class="step-text">找到营销素材入口，浏览可用素材</text>
  
  <!-- 步骤b -->
  <rect x="200" y="380" width="1520" height="80" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <circle cx="280" cy="420" r="25" fill="#059669"/>
  <text x="280" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">b</text>
  <text x="330" y="410" class="step-title" fill="#059669">选择一个"XX活动"的海报模板</text>
  <text x="330" y="440" class="step-text">根据当前活动选择合适的宣传素材</text>
  
  <!-- 步骤c -->
  <rect x="200" y="480" width="1520" height="80" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <circle cx="280" cy="520" r="25" fill="#EA580C"/>
  <text x="280" y="530" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">c</text>
  <text x="330" y="510" class="step-title" fill="#EA580C">点击"分享到朋友圈"</text>
  <text x="330" y="540" class="step-text">系统自动跳转到微信分享界面</text>
  
  <!-- 步骤d -->
  <rect x="200" y="580" width="1520" height="100" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <circle cx="280" cy="630" r="25" fill="#7C3AED"/>
  <text x="280" y="640" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">d</text>
  <text x="330" y="610" class="step-title" fill="#7C3AED">搭配一句吸引人的文案</text>
  <text x="330" y="640" class="step-text">(如"移动爱家XX活动开始啦！优惠多多，快来找我办理！")</text>
  <text x="330" y="665" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280;">个性化文案增加亲和力</text>
  
  <!-- 步骤e -->
  <rect x="200" y="700" width="1520" height="80" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <circle cx="280" cy="740" r="25" fill="#F59E0B"/>
  <text x="280" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: white; text-anchor: middle;">e</text>
  <text x="330" y="730" class="step-title" fill="#F59E0B">发布！</text>
  <text x="330" y="760" class="step-text">完成分享，开始营销推广</text>
  
  <!-- 鼓励 -->
  <rect x="200" y="860" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="910" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">[鼓励]：</text>
  <text x="960" y="950" class="encourage-text" fill="#1E3A8A">养成利用碎片时间，通过平台素材进行宣传的好习惯</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">每一次分享都是品牌传播，每一次传播都可能带来商机</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="400" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="400" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
