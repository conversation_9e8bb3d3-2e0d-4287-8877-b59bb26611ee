<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .feature-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #059669; }
      .purpose-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #7C3AED; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">AI"监理"，助你规范操作</text>
  <text x="960" y="140" class="subtitle-text">好帮手：AI质检小程序</text>
  
  <!-- 怎么用 -->
  <rect x="100" y="180" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">怎么用？</text>
  <text x="150" y="280" class="content-text">按照要求，用手机对着特定位置（如熔纤盘、设备标签）拍照上传。</text>
  <text x="150" y="320" class="step-text">📱 操作简单：拍照 → 上传 → AI分析 → 即时反馈</text>
  <text x="150" y="360" class="step-text">⏱️ 几秒钟就能得到质检结果</text>
  
  <!-- AI能力展示 -->
  <rect x="100" y="410" width="1720" height="250" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="460" class="section-title" fill="#059669">能干啥？ AI能自动识别：</text>
  
  <!-- 识别项目 -->
  <rect x="200" y="500" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="375" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">线路整理</text>
  <text x="220" y="570" class="feature-text">✓ 线路是否整齐？</text>
  <text x="220" y="600" class="feature-text">✓ 弯曲半径是否合适？</text>
  
  <rect x="600" y="500" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="775" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">标签规范</text>
  <text x="620" y="570" class="feature-text">✓ 标签是否规范？</text>
  <text x="620" y="600" class="feature-text">✓ 信息是否完整？</text>
  
  <rect x="1000" y="500" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1175" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">工具摆放</text>
  <text x="1020" y="570" class="feature-text">✓ 工具是否摆放有序？</text>
  <text x="1020" y="600" class="feature-text">✓ 现场是否整洁？</text>
  
  <rect x="1400" y="500" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1575" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">安全防护</text>
  <text x="1420" y="570" class="feature-text">✓ 防护措施是否到位？</text>
  <text x="1420" y="600" class="feature-text">✓ 安全规范是否遵守？</text>
  
  <!-- 结果对比 -->
  <rect x="150" y="700" width="400" height="200" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="350" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">✅ 合格</text>
  <text x="170" y="780" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 线路整理规范</text>
  <text x="170" y="810" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 标签信息完整</text>
  <text x="170" y="840" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 现场整洁有序</text>
  <text x="350" y="880" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">继续保持！</text>
  
  <text x="750" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #6B7280; text-anchor: middle;">VS</text>
  
  <rect x="950" y="700" width="400" height="200" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1150" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">❌ 不合格</text>
  <text x="970" y="780" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 线路缠绕混乱</text>
  <text x="970" y="810" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 标签缺失或模糊</text>
  <text x="970" y="840" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 工具摆放杂乱</text>
  <text x="1150" y="880" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #DC2626; text-anchor: middle;">需要整改！</text>
  
  <!-- 目的 -->
  <rect x="1400" y="700" width="420" height="200" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="1610" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">🎯 目的</text>
  <text x="1420" y="780" class="purpose-text">提醒我们注意细节</text>
  <text x="1420" y="810" class="purpose-text">养成标准化操作</text>
  <text x="1420" y="840" class="purpose-text">习惯</text>
  <text x="1610" y="880" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">AI助力成长！</text>
  
  <!-- 底部提醒 -->
  <rect x="200" y="930" width="1520" height="100" fill="#F8FAFC" rx="15" stroke="#6B7280" stroke-width="2"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">🤖 AI是助手，不是监督</text>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">帮助我们发现问题，提升专业水平！</text>
</svg>
