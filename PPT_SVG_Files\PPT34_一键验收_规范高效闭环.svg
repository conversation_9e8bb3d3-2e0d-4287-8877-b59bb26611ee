<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .benefit-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; font-style: italic; }
      .note-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">电子签名，服务完成的标志</text>
  <text x="960" y="140" class="subtitle-text">"一键验收"：规范高效的闭环</text>
  
  <!-- 为何要电子验收 -->
  <rect x="100" y="180" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">为何要电子验收？</text>
  
  <!-- 优势展示 -->
  <rect x="200" y="270" width="280" height="80" fill="#DBEAFE" rx="10"/>
  <text x="340" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">快捷</text>
  
  <rect x="520" y="270" width="280" height="80" fill="#DBEAFE" rx="10"/>
  <text x="660" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">防丢失</text>
  
  <rect x="840" y="270" width="280" height="80" fill="#DBEAFE" rx="10"/>
  <text x="980" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">易查询</text>
  
  <rect x="1160" y="270" width="280" height="80" fill="#DBEAFE" rx="10"/>
  <text x="1300" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">更环保</text>
  
  <rect x="1480" y="270" width="280" height="80" fill="#DBEAFE" rx="10"/>
  <text x="1620" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">数据实时</text>
  
  <!-- 引导话术 -->
  <rect x="100" y="410" width="1720" height="200" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="460" class="section-title" fill="#059669">引导话术：</text>
  
  <!-- 话术气泡 -->
  <rect x="200" y="500" width="1520" height="80" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="530" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">💬 标准话术：</text>
  <text x="220" y="570" class="speech-text">"X先生，服务都完成了，麻烦您在手机上帮我确认一下，</text>
  <text x="220" y="590" class="speech-text">这样系统里就记录服务结束了。"</text>
  
  <!-- 注意事项 -->
  <rect x="100" y="640" width="1720" height="300" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <text x="150" y="690" class="section-title" fill="#EA580C">注意事项：</text>
  
  <!-- 注意事项列表 -->
  <rect x="200" y="730" width="480" height="180" fill="#FED7AA" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="440" y="770" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C; text-anchor: middle;">网络通畅</text>
  <text x="220" y="810" class="note-text">✓ 确保现场网络正常</text>
  <text x="220" y="840" class="note-text">✓ 4G/5G信号良好</text>
  <text x="220" y="870" class="note-text">✓ 避免网络中断</text>
  
  <rect x="720" y="730" width="480" height="180" fill="#FED7AA" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="960" y="770" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C; text-anchor: middle;">指导操作</text>
  <text x="740" y="810" class="note-text">✓ 耐心指导用户</text>
  <text x="740" y="840" class="note-text">✓ 演示签名步骤</text>
  <text x="740" y="870" class="note-text">✓ 协助完成操作</text>
  
  <rect x="1240" y="730" width="480" height="180" fill="#FED7AA" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="1480" y="770" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C; text-anchor: middle;">确认提交</text>
  <text x="1260" y="810" class="note-text">✓ 签名完成后检查</text>
  <text x="1260" y="840" class="note-text">✓ 确认提交成功</text>
  <text x="1260" y="870" class="note-text">✓ 系统显示完成</text>
  
  <!-- 操作流程示意 -->
  <rect x="200" y="970" width="1520" height="80" fill="#F8FAFC" rx="15" stroke="#6B7280" stroke-width="2"/>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">📱 操作流程</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">打开验收界面 → 用户签名确认 → 系统自动提交 → 服务完成闭环</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="280" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="280" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="550" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="550" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="820" r="35" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="820" r="35" fill="#EA580C" opacity="0.3"/>
</svg>
