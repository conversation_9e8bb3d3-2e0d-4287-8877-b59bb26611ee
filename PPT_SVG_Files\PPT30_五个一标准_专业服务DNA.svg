<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .standard-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .standard-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; text-anchor: middle; fill: #4B5563; }
      .standard-detail { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #6B7280; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">"五个一"：刻在DNA里的服务标准</text>
  <text x="960" y="140" class="subtitle-text">服务标准核心："五个一"动作分解</text>
  
  <!-- 一套工具 -->
  <rect x="100" y="200" width="320" height="300" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="260" y="240" class="icon-text">🧰</text>
  <text x="260" y="290" class="standard-title" fill="#3B82F6">一套工具</text>
  <text x="260" y="330" class="standard-desc">工具齐全</text>
  <text x="260" y="360" class="standard-desc">摆放有序</text>
  <text x="260" y="390" class="standard-desc">专业！</text>
  <text x="120" y="430" class="standard-detail">• 光功率计、测速设备</text>
  <text x="120" y="455" class="standard-detail">• 熔接工具、标签打印机</text>
  <text x="120" y="480" class="standard-detail">• 整洁摆放，一目了然</text>
  
  <!-- 一次测速 -->
  <rect x="460" y="200" width="320" height="300" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="620" y="240" class="icon-text">📶</text>
  <text x="620" y="290" class="standard-title" fill="#059669">一次测速</text>
  <text x="620" y="330" class="standard-desc">核心区域</text>
  <text x="620" y="360" class="standard-desc">当面演示</text>
  <text x="620" y="390" class="standard-desc">达标！</text>
  <text x="480" y="430" class="standard-detail">• 客厅、主卧等关键位置</text>
  <text x="480" y="455" class="standard-detail">• 使用爱家APP测速</text>
  <text x="480" y="480" class="standard-detail">• 让用户亲眼看到结果</text>
  
  <!-- 一份指引 -->
  <rect x="820" y="200" width="320" height="300" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="980" y="240" class="icon-text">📋</text>
  <text x="980" y="290" class="standard-title" fill="#EA580C">一份指引</text>
  <text x="980" y="330" class="standard-desc">WiFi密码</text>
  <text x="980" y="360" class="standard-desc">APP用法</text>
  <text x="980" y="390" class="standard-desc">讲清！</text>
  <text x="840" y="430" class="standard-detail">• WiFi名称和密码卡片</text>
  <text x="840" y="455" class="standard-detail">• 爱家APP功能介绍</text>
  <text x="840" y="480" class="standard-detail">• 基本操作演示</text>
  
  <!-- 一键验收 -->
  <rect x="1180" y="200" width="320" height="300" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="1340" y="240" class="icon-text">✍️</text>
  <text x="1340" y="290" class="standard-title" fill="#7C3AED">一键验收</text>
  <text x="1340" y="330" class="standard-desc">流程规范</text>
  <text x="1340" y="360" class="standard-desc">数据闭环</text>
  <text x="1340" y="390" class="standard-desc">高效！</text>
  <text x="1200" y="430" class="standard-detail">• 电子签名确认</text>
  <text x="1200" y="455" class="standard-detail">• 系统自动同步</text>
  <text x="1200" y="480" class="standard-detail">• 规范化流程</text>
  
  <!-- 一句关怀 -->
  <rect x="540" y="540" width="320" height="300" fill="#FDF2F8" rx="20" stroke="#EC4899" stroke-width="3"/>
  <text x="700" y="580" class="icon-text">💝</text>
  <text x="700" y="630" class="standard-title" fill="#EC4899">一句关怀</text>
  <text x="700" y="670" class="standard-desc">温馨提示</text>
  <text x="700" y="700" class="standard-desc">礼貌道别</text>
  <text x="700" y="730" class="standard-desc">暖心！</text>
  <text x="560" y="770" class="standard-detail">• "有问题随时联系我们"</text>
  <text x="560" y="795" class="standard-detail">• "感谢您的配合"</text>
  <text x="560" y="820" class="standard-detail">• 留下专业好印象</text>
  
  <!-- 连接线 -->
  <path d="M 420 350 L 460 350" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="5,5"/>
  <path d="M 780 350 L 820 350" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="5,5"/>
  <path d="M 1140 350 L 1180 350" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="5,5"/>
  <path d="M 1340 500 L 1340 540 L 860 540 L 860 540" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="5,5"/>
  <path d="M 260 500 L 260 540 L 540 540" fill="none" stroke="#6B7280" stroke-width="4" stroke-dasharray="5,5"/>
  
  <!-- 底部强调 -->
  <rect x="200" y="880" width="1520" height="120" fill="#F8FAFC" rx="25" stroke="#1E3A8A" stroke-width="3"/>
  <text x="960" y="920" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">🎯 核心理念</text>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">每一个"一"都是专业的体现，用户满意的保障！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#7C3AED" opacity="0.3"/>
  <circle cx="50" cy="690" r="25" fill="#EC4899" opacity="0.3"/>
  <circle cx="1870" cy="690" r="25" fill="#059669" opacity="0.3"/>
</svg>
