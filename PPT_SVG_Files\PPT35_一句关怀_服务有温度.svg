<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .element-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">告别话术，传递关怀</text>
  <text x="960" y="140" class="subtitle-text">"一句关怀"：服务有温度</text>
  
  <!-- 时机 -->
  <rect x="100" y="180" width="1720" height="120" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">时机：</text>
  <text x="150" y="270" class="content-text">所有服务完成，即将离开用户家时。</text>
  
  <!-- 核心要素 -->
  <rect x="100" y="330" width="1720" height="200" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="380" class="section-title" fill="#059669">核心：</text>
  
  <!-- 四个要素 -->
  <rect x="200" y="420" width="350" height="80" fill="#DCFCE7" rx="10"/>
  <text x="375" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">表达感谢</text>
  
  <text x="580" y="460" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="620" y="420" width="350" height="80" fill="#DCFCE7" rx="10"/>
  <text x="795" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">使用提醒</text>
  
  <text x="1000" y="460" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="1040" y="420" width="350" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1215" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">问题求助渠道</text>
  
  <text x="1420" y="460" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="1460" y="420" width="260" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1590" y="470" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">祝福</text>
  
  <!-- 话术库 -->
  <rect x="100" y="560" width="1720" height="400" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <text x="150" y="610" class="section-title" fill="#EA580C">话术库：</text>
  
  <!-- 标准话术 -->
  <rect x="200" y="650" width="1520" height="120" fill="#FED7AA" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="690" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">💬 标准版：</text>
  <text x="220" y="720" class="speech-text">"感谢您的配合！网络可以正常使用了，后续有问题随时打我们电话</text>
  <text x="220" y="750" class="speech-text">或用爱家APP联系。祝您用网愉快！"</text>
  
  <!-- 增值推荐版 -->
  <rect x="200" y="790" width="1520" height="140" fill="#FED7AA" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="830" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">💬 增值推荐版：</text>
  <text x="220" y="860" class="speech-text">"都弄好了，您可以试试。如果感觉哪个房间信号还是差点意思，</text>
  <text x="220" y="890" class="speech-text">可以考虑加个WiFi扩展器，效果更好。先这样用着，有问题随时找我们。"</text>
  <text x="220" y="920" class="highlight-text" fill="#7C3AED">(结合潜在需求)</text>
  
  <!-- 温度体现 -->
  <rect x="200" y="980" width="1520" height="80" fill="#F8FAFC" rx="15" stroke="#6B7280" stroke-width="2"/>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">💝 温度体现</text>
  <text x="960" y="1040" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; text-anchor: middle;">一句贴心话，让用户感受到移动服务的温度！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="240" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="240" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="460" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="460" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="760" r="35" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="760" r="35" fill="#EA580C" opacity="0.3"/>
  
  <!-- 心形装饰 -->
  <text x="50" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #EC4899;">💝</text>
  <text x="1870" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #EC4899;">💝</text>
</svg>
