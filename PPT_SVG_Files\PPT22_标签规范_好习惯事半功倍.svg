<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .requirement-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .benefit-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #059669; }
      .label-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">随手一贴，方便大家</text>
  <text x="960" y="140" class="subtitle-text">施工核心指标4：标签规范 (好习惯事半功倍)</text>
  
  <!-- 要求 -->
  <rect x="100" y="180" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">要求：</text>
  <text x="150" y="280" class="content-text">在光猫、面板等关键位置，务必粘贴打印好的、</text>
  <text x="150" y="320" class="content-text">包含清晰用户地址信息的标签。</text>
  <text x="150" y="360" class="requirement-text">📋 标签内容：用户姓名、地址、联系电话、安装日期</text>
  
  <!-- 标签示例 -->
  <rect x="200" y="420" width="600" height="300" fill="#F8FAFC" rx="20" stroke="#6B7280" stroke-width="2"/>
  <text x="500" y="460" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">标准设备标签</text>
  
  <!-- 模拟标签 -->
  <rect x="250" y="490" width="500" height="200" fill="white" rx="10" stroke="#3B82F6" stroke-width="2"/>
  <text x="270" y="520" class="label-text">用户：张先生</text>
  <text x="270" y="550" class="label-text">地址：锦江区春熙路123号2-3-401</text>
  <text x="270" y="580" class="label-text">电话：138****8888</text>
  <text x="270" y="610" class="label-text">安装日期：2025-08-24</text>
  <text x="270" y="640" class="label-text">装维师傅：李师傅</text>
  <text x="270" y="670" class="label-text">备注：客厅光猫</text>
  
  <!-- 意义 -->
  <rect x="850" y="420" width="870" height="300" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="880" y="470" class="section-title" fill="#059669">意义：</text>
  <text x="880" y="520" class="benefit-text">✓ 方便自己后续维护时快速识别</text>
  <text x="880" y="560" class="benefit-text">✓ 方便同事接手时了解情况</text>
  <text x="880" y="600" class="benefit-text">✓ 提高排障效率，减少重复询问</text>
  <text x="880" y="640" class="benefit-text">✓ 体现专业形象，用户更信任</text>
  <text x="880" y="680" class="benefit-text">✓ 规范管理，便于追溯和统计</text>
  
  <!-- 对比展示 -->
  <rect x="150" y="760" width="400" height="200" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="350" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">有标签</text>
  <text x="170" y="840" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 信息清晰明确</text>
  <text x="170" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 维护高效便捷</text>
  <text x="170" y="900" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 专业形象加分</text>
  <text x="350" y="940" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #059669; text-anchor: middle;">👍 规范专业</text>
  
  <text x="750" y="860" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #6B7280; text-anchor: middle;">VS</text>
  
  <rect x="950" y="760" width="400" height="200" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1150" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">无标签</text>
  <text x="970" y="840" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 信息不明确</text>
  <text x="970" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 维护效率低</text>
  <text x="970" y="900" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">• 显得不专业</text>
  <text x="1150" y="940" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #DC2626; text-anchor: middle;">👎 马虎应付</text>
  
  <!-- 温馨提示 -->
  <rect x="1400" y="760" width="420" height="200" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <text x="1610" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">💡 小贴士</text>
  <text x="1420" y="840" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">使用防水标签纸</text>
  <text x="1420" y="870" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">字迹清晰工整</text>
  <text x="1420" y="900" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: normal; fill: #374151;">粘贴位置合适</text>
  <text x="1610" y="940" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">细节体现专业！</text>
  
  <!-- 底部提醒 -->
  <rect x="200" y="990" width="1520" height="70" fill="#EFF6FF" rx="15"/>
  <text x="960" y="1035" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">小标签，大作用！养成好习惯，工作更轻松！</text>
</svg>
