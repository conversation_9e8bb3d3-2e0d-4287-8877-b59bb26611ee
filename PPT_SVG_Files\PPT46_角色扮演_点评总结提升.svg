<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .point-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">总结提升，共同成长</text>
  <text x="960" y="140" class="subtitle-text">角色扮演：点评总结</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">📝</text>
  
  <!-- 点评维度 -->
  <rect x="100" y="300" width="1720" height="350" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">点评维度</text>
  
  <!-- 优点肯定 -->
  <rect x="200" y="390" width="350" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="375" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">优点肯定</text>
  <text x="220" y="460" class="point-text">• 表现出色的地方</text>
  <text x="220" y="485" class="point-text">• 话术运用得当</text>
  
  <!-- 不足指出 -->
  <rect x="600" y="390" width="350" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="775" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C; text-anchor: middle;">不足指出</text>
  <text x="620" y="460" class="point-text">• 需要改进的方面</text>
  <text x="620" y="485" class="point-text">• 遗漏的关键步骤</text>
  
  <!-- 改进建议 -->
  <rect x="1000" y="390" width="350" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="1175" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">改进建议</text>
  <text x="1020" y="460" class="point-text">• 具体改进方法</text>
  <text x="1020" y="485" class="point-text">• 更好的处理方式</text>
  
  <!-- 经验分享 -->
  <rect x="1400" y="390" width="350" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="1575" y="430" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">经验分享</text>
  <text x="1420" y="460" class="point-text">• 实际工作经验</text>
  <text x="1420" y="485" class="point-text">• 成功案例分享</text>
  
  <!-- 点评示例 -->
  <rect x="200" y="530" width="1520" height="100" fill="#FFFBEB" rx="15"/>
  <text x="220" y="570" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B;">💬 点评示例：</text>
  <text x="220" y="600" class="content-text">"小李同学共情做得很好，能耐心听用户说完。建议在分析问题时可以更详细一些，</text>
  <text x="220" y="625" class="content-text">让用户更清楚地了解问题原因和解决方案。"</text>
  
  <!-- 总结要点 -->
  <rect x="100" y="680" width="1720" height="250" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="730" class="section-title" fill="#059669">总结要点：</text>
  
  <!-- 关键收获 -->
  <rect x="200" y="770" width="700" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="550" y="810" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">关键收获</text>
  <text x="220" y="840" class="point-text">• 投诉处理四步法的实际运用</text>
  <text x="220" y="870" class="point-text">• 不同场景的应对策略</text>
  
  <!-- 持续改进 -->
  <rect x="950" y="770" width="700" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1300" y="810" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">持续改进</text>
  <text x="970" y="840" class="point-text">• 在实际工作中继续练习</text>
  <text x="970" y="870" class="point-text">• 同事间互相学习交流</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="960" width="1520" height="100" fill="#EFF6FF" rx="20"/>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">💡 核心理念</text>
  <text x="960" y="1020" class="highlight-text" fill="#059669" text-anchor="middle">演练不是表演，是为了在真实场景中更好地服务用户</text>
  <text x="960" y="1050" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">每一次练习都是成长的机会</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="450" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="450" r="30" fill="#EA580C" opacity="0.3"/>
  <circle cx="50" cy="830" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="830" r="25" fill="#059669" opacity="0.3"/>
</svg>
