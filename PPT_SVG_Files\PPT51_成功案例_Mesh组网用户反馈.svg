<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .feedback-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: normal; fill: #374151; font-style: italic; }
      .effect-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">用户说好，才是真的好！</text>
  <text x="960" y="140" class="subtitle-text">成功案例：Mesh组网的用户反馈</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">👍</text>
  
  <!-- 用户反馈区域 -->
  <rect x="100" y="300" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="350" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">真实用户好评</text>
  
  <!-- 反馈1 -->
  <rect x="200" y="390" width="1520" height="100" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="420" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">⭐⭐⭐⭐⭐ 张先生（复式别墅）</text>
  <text x="220" y="450" class="feedback-text">"装了你们推荐的Mesh，家里终于没有信号死角了，太棒了！"</text>
  <text x="220" y="480" class="feedback-text">"三层楼每个房间信号都满格，孩子在三楼上网课也不卡了。"</text>
  
  <!-- 反馈2 -->
  <rect x="200" y="510" width="1520" height="100" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="540" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #3B82F6;">⭐⭐⭐⭐⭐ 李女士（大平层）</text>
  <text x="220" y="570" class="feedback-text">"以前在二楼视频通话老是断，现在一点问题都没有，谢谢师傅！"</text>
  <text x="220" y="600" class="feedback-text">"而且手机在家里走动，网络切换很顺畅，体验比以前好太多了。"</text>
  
  <!-- 反馈3 -->
  <rect x="200" y="630" width="1520" height="100" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="660" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #EA580C;">⭐⭐⭐⭐⭐ 王总（智能家居爱好者）</text>
  <text x="220" y="690" class="feedback-text">"家里智能设备多，以前经常掉线，现在稳定多了。"</text>
  <text x="220" y="720" class="feedback-text">"摄像头、音响、扫地机器人都连得很稳，智能家居终于好用了！"</text>
  
  <!-- 反馈4 -->
  <rect x="200" y="750" width="1520" height="100" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="220" y="780" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #7C3AED;">⭐⭐⭐⭐⭐ 陈先生（游戏玩家）</text>
  <text x="220" y="810" class="feedback-text">"游戏延迟明显降低了，在书房打游戏也不掉线了。"</text>
  <text x="220" y="840" class="feedback-text">"朋友来家里都夸我家网络好，问我怎么弄的，哈哈！"</text>
  
  <!-- 效果总结 -->
  <rect x="100" y="930" width="1720" height="120" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="3"/>
  <text x="150" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #F59E0B;">[效果]：</text>
  <text x="300" y="980" class="effect-text">提升用户满意度</text>
  <text x="600" y="980" class="effect-text">增加ARPU值</text>
  <text x="900" y="980" class="effect-text">体现专业服务价值</text>
  <text x="150" y="1020" class="highlight-text" fill="#059669">用户满意 → 口碑传播 → 业务增长 → 多方共赢</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="450" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="450" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#7C3AED" opacity="0.3"/>
  
  <!-- 好评图标装饰 -->
  <text x="50" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #F59E0B;">⭐</text>
  <text x="1870" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #F59E0B;">⭐</text>
</svg>
