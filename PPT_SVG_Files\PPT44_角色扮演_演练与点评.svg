<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .point-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">实战演练，提升技能</text>
  <text x="960" y="140" class="subtitle-text">角色扮演：演练与点评</text>
  
  <!-- 主图标 -->
  <text x="960" y="250" class="icon-text">🎭</text>
  
  <!-- 演练说明 -->
  <rect x="100" y="300" width="1720" height="150" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="350" class="section-title" fill="#3B82F6">演练说明：</text>
  <text x="150" y="390" class="content-text">分组进行，模拟真实投诉场景</text>
  <text x="150" y="430" class="content-text">每组2-3人，一人扮演用户，一人扮演装维师傅，一人观察记录</text>
  
  <!-- 演练重点 -->
  <rect x="100" y="480" width="1720" height="350" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="530" class="section-title" fill="#059669">演练重点：</text>
  
  <!-- 四个重点 -->
  <rect x="200" y="570" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="375" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">倾听与共情</text>
  <text x="220" y="640" class="point-text">• 耐心听完用户抱怨</text>
  <text x="220" y="670" class="point-text">• 表达理解和同情</text>
  
  <rect x="600" y="570" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="775" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">标准话术运用</text>
  <text x="620" y="640" class="point-text">• 灵活运用学过的话术</text>
  <text x="620" y="670" class="point-text">• 语言自然不生硬</text>
  
  <rect x="1000" y="570" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1175" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">问题分析能力</text>
  <text x="1020" y="640" class="point-text">• 快速找到问题根源</text>
  <text x="1020" y="670" class="point-text">• 提供可行解决方案</text>
  
  <rect x="1400" y="570" width="350" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1575" y="610" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">情绪控制</text>
  <text x="1420" y="640" class="point-text">• 保持冷静专业</text>
  <text x="1420" y="670" class="point-text">• 不被用户情绪影响</text>
  
  <!-- 场景示例 -->
  <rect x="200" y="710" width="1520" height="100" fill="#E0F2FE" rx="15"/>
  <text x="220" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #0284C7;">🎬 场景示例：</text>
  <text x="220" y="780" class="content-text">"师傅，你们装的网络太差了！卧室根本没信号，看个视频都卡得要死！"</text>
  
  <!-- 点评环节 -->
  <rect x="100" y="860" width="1720" height="150" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <text x="150" y="910" class="section-title" fill="#EA580C">点评环节：</text>
  <text x="150" y="950" class="content-text">讲师和其他学员进行点评</text>
  <text x="150" y="990" class="point-text">• 优点肯定  • 不足指出  • 改进建议  • 经验分享</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="1030" width="1520" height="40" fill="#F8FAFC" rx="15"/>
  <text x="960" y="1055" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">实践是最好的老师，演练让技能更扎实！</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="375" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="375" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="630" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="630" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="935" r="35" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="935" r="35" fill="#EA580C" opacity="0.3"/>
</svg>
