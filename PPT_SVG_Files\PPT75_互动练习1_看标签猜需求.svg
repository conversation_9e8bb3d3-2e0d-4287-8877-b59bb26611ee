<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .exercise-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .label-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
      .instruction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; }
      .answer-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">我是"金牌"维系能手</text>
  <text x="960" y="140" class="subtitle-text">互动练习1：看标签猜需求</text>
  
  <!-- 练习说明 -->
  <rect x="100" y="180" width="1720" height="100" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="exercise-title" fill="#3B82F6">练习说明：</text>
  <text x="150" y="260" class="instruction-text">给出用户标签，让学员说出对应的营销机会点</text>
  
  <!-- 练习内容 -->
  <rect x="100" y="310" width="1720" height="600" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  
  <!-- 标签1：高流量用户 -->
  <rect x="200" y="360" width="700" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="550" y="400" class="label-text" fill="#3B82F6">标签：高流量用户</text>
  <text x="220" y="430" class="instruction-text">特征：每月流量消耗接近或超过套餐限额</text>
  <text x="220" y="460" class="answer-text">营销机会：→ 推荐升档套餐/无限流量包/家庭云存储</text>
  
  <!-- 标签2：低活跃用户 -->
  <rect x="950" y="360" width="700" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1300" y="400" class="label-text" fill="#059669">标签：低活跃用户</text>
  <text x="970" y="430" class="instruction-text">特征：长期不使用或使用频率很低</text>
  <text x="970" y="460" class="answer-text">营销机会：→ 主动关怀/福利提醒/活动引导</text>
  
  <!-- 标签3：大户型用户 -->
  <rect x="200" y="500" width="700" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="550" y="540" class="label-text" fill="#EA580C">标签：大户型用户</text>
  <text x="220" y="570" class="instruction-text">特征：房屋面积大，可能有WiFi覆盖问题</text>
  <text x="220" y="600" class="answer-text">营销机会：→ 推荐Mesh组网/WiFi扩展器</text>
  
  <!-- 标签4：合约到期用户 -->
  <rect x="950" y="500" width="700" height="120" fill="#F3E8FF" rx="15" stroke="#7C3AED" stroke-width="2"/>
  <text x="1300" y="540" class="label-text" fill="#7C3AED">标签：合约到期用户</text>
  <text x="970" y="570" class="instruction-text">特征：合约即将到期，需要续约</text>
  <text x="970" y="600" class="answer-text">营销机会：→ 提前触达/续约优惠/升档推荐</text>
  
  <!-- 标签5：智能设备多 -->
  <rect x="200" y="640" width="700" height="120" fill="#FFFBEB" rx="15" stroke="#F59E0B" stroke-width="2"/>
  <text x="550" y="680" class="label-text" fill="#F59E0B">标签：智能设备多</text>
  <text x="220" y="710" class="instruction-text">特征：家中智能家居设备数量较多</text>
  <text x="220" y="740" class="answer-text">营销机会：→ 推荐千兆宽带/Mesh组网/家庭云</text>
  
  <!-- 标签6：潜在流失 -->
  <rect x="950" y="640" width="700" height="120" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1300" y="680" class="label-text" fill="#DC2626">标签：潜在流失</text>
  <text x="970" y="710" class="instruction-text">特征：系统预警显示流失风险高</text>
  <text x="970" y="740" class="answer-text">营销机会：→ 立即挽留/问题解决/补偿措施</text>
  
  <!-- 练习要求 -->
  <rect x="200" y="940" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">练习要求</text>
  <text x="960" y="1000" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #374151; text-anchor: middle;">根据用户标签特征，快速识别营销机会点，并说出推荐理由</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 26px; font-weight: normal; fill: #6B7280; text-anchor: middle;">培养敏锐的商机嗅觉，提升精准营销能力</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="230" r="30" fill="#3B82F6" opacity="0.3"/>
</svg>
