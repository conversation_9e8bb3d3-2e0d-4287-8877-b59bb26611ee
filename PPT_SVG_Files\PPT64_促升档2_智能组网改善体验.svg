<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .signal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .logic-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">WiFi不好也是升档机会！推荐Mesh组网</text>
  <text x="960" y="140" class="subtitle-text">"促升档"2：智能组网，改善体验促价值</text>
  
  <!-- 识别信号 -->
  <rect x="100" y="180" width="1720" height="250" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#DC2626">识别信号 (复习)：</text>
  
  <!-- 信号展示 -->
  <rect x="200" y="270" width="480" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="440" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">用户抱怨</text>
  <text x="220" y="340" class="signal-text">信号差、有死角</text>
  <text x="220" y="370" class="signal-text">WiFi经常断线</text>
  
  <rect x="720" y="270" width="480" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="960" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">户型特点</text>
  <text x="740" y="340" class="signal-text">大户型、复式结构</text>
  <text x="740" y="370" class="signal-text">墙体较厚，阻挡严重</text>
  
  <rect x="1240" y="270" width="480" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1480" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">设备环境</text>
  <text x="1260" y="340" class="signal-text">智能家居设备多</text>
  <text x="1260" y="370" class="signal-text">多人同时使用</text>
  
  <!-- 推荐逻辑 -->
  <rect x="100" y="460" width="1720" height="200" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="510" class="section-title" fill="#059669">推荐逻辑：</text>
  
  <!-- 逻辑流程 -->
  <rect x="200" y="550" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="340" y="595" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">解决用户痛点<br/>(信号差)</text>
  
  <text x="510" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="550" y="550" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="690" y="595" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">提供解决方案<br/>(Mesh)</text>
  
  <text x="860" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="900" y="550" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1040" y="595" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #059669; text-anchor: middle;">关联套餐优惠</text>
  
  <text x="1210" y="590" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="1250" y="550" width="280" height="80" fill="#DCFCE7" rx="10"/>
  <text x="1390" y="580" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">提升用户体验</text>
  <text x="1390" y="605" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 20px; font-weight: bold; fill: #059669; text-anchor: middle;">和ARPU</text>
  
  <!-- 话术回顾 -->
  <rect x="100" y="690" width="1720" height="200" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="740" class="section-title" fill="#3B82F6">话术回顾：</text>
  
  <rect x="200" y="780" width="1520" height="80" fill="#DBEAFE" rx="15"/>
  <text x="220" y="810" class="speech-text">"您家这个情况，用Mesh路由器能保证全屋信号都好，</text>
  <text x="220" y="840" class="speech-text">走到哪网速都快。现在办理有优惠套餐，还送安装调试…"</text>
  
  <!-- 价值强调 -->
  <rect x="200" y="920" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">价值提升</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">解决痛点 + 提升体验 + 增加收入 = 三赢局面</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">用户满意，业务增长，服务价值体现</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="320" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="320" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="50" cy="590" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="590" r="25" fill="#059669" opacity="0.3"/>
</svg>
