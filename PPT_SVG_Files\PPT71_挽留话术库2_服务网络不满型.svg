<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .point-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">用户不满意？道歉+解决+补偿</text>
  <text x="960" y="140" class="subtitle-text">挽留话术库2：应对"服务/网络不满型"</text>
  
  <!-- 沟通要点 -->
  <rect x="100" y="180" width="1720" height="750" fill="#F8FAFC" rx="25" stroke="#DC2626" stroke-width="4"/>
  <text x="150" y="230" class="section-title" fill="#DC2626">沟通要点：</text>
  
  <!-- 真诚道歉 -->
  <rect x="200" y="270" width="1520" height="120" fill="#FEF2F2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="220" y="310" class="point-title" fill="#DC2626">真诚道歉是前提：</text>
  <text x="220" y="340" class="speech-text">"非常抱歉之前的服务/网络给您带来不好的体验…"</text>
  <text x="220" y="370" class="content-text">态度要诚恳，不要推诿责任</text>
  
  <!-- 承诺解决 -->
  <rect x="200" y="410" width="1520" height="140" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="450" class="point-title" fill="#059669">承诺解决：</text>
  <text x="220" y="480" class="speech-text">"您反映的[具体问题]，我们一定重视并尽快解决。</text>
  <text x="220" y="510" class="speech-text">我立刻帮您[安排上门检测/上报后台处理]…"</text>
  <text x="220" y="540" class="content-text">给出具体的解决时间和行动计划</text>
  
  <!-- 给予补偿 -->
  <rect x="200" y="570" width="1520" height="140" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="220" y="610" class="point-title" fill="#3B82F6">给予补偿：</text>
  <text x="220" y="640" class="speech-text">"为了弥补给您带来的不便，我们为您申请了</text>
  <text x="220" y="670" class="speech-text">[补偿措施，如免费提速体验/赠送服务时长/小礼品]…"</text>
  <text x="220" y="700" class="content-text">补偿要实实在在，让用户感受到诚意</text>
  
  <!-- 后续跟进 -->
  <rect x="200" y="730" width="1520" height="100" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="220" y="770" class="point-title" fill="#EA580C">后续跟进：</text>
  <text x="220" y="800" class="speech-text">"问题解决后我会再联系您确认一下效果。"</text>
  
  <!-- 处理原则 -->
  <rect x="200" y="850" width="1520" height="60" fill="#FFFBEB" rx="15"/>
  <text x="220" y="890" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B;">处理原则：</text>
  <text x="400" y="890" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626;">先道歉，再解决，后补偿，必跟进</text>
  
  <!-- 核心策略 -->
  <rect x="200" y="930" width="1520" height="100" fill="#F0FDF4" rx="20"/>
  <text x="960" y="960" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">核心策略</text>
  <text x="960" y="990" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">用行动证明诚意，用结果赢回信任</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">服务问题要用服务来解决</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#EA580C" opacity="0.3"/>
</svg>
