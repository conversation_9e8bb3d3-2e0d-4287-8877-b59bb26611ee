<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .process-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; text-anchor: middle; }
      .key-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #4B5563; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">"信号不好"怎么说？—— 专业诊断给方案</text>
  <text x="960" y="140" class="subtitle-text">话术库：应对信号差/网速慢</text>
  
  <!-- 应对结构回顾 -->
  <rect x="100" y="180" width="1720" height="120" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">回顾应对结构：</text>
  
  <!-- 五步流程 -->
  <rect x="200" y="260" width="240" height="30" fill="#DBEAFE" rx="5"/>
  <text x="320" y="280" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">倾听</text>
  
  <text x="470" y="275" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="500" y="260" width="240" height="30" fill="#DBEAFE" rx="5"/>
  <text x="620" y="280" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">检测</text>
  
  <text x="770" y="275" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="800" y="260" width="240" height="30" fill="#DBEAFE" rx="5"/>
  <text x="920" y="280" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">分析</text>
  
  <text x="1070" y="275" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="1100" y="260" width="240" height="30" fill="#DBEAFE" rx="5"/>
  <text x="1220" y="280" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">方案</text>
  
  <text x="1370" y="275" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">→</text>
  
  <rect x="1400" y="260" width="240" height="30" fill="#DBEAFE" rx="5"/>
  <text x="1520" y="280" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 24px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">验证</text>
  
  <!-- 关键话术 -->
  <rect x="100" y="330" width="1720" height="450" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="380" class="section-title" fill="#059669">关键话术：</text>
  
  <!-- 分析原因话术 -->
  <rect x="200" y="420" width="1520" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="460" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">🔍 分析原因时通俗易懂：</text>
  <text x="220" y="490" class="speech-text">"我测试了一下，主要是路由器离得远/墙体遮挡比较厚，</text>
  <text x="220" y="520" class="speech-text">信号穿透过来就弱了一些。"</text>
  
  <!-- 提供方案话术 -->
  <rect x="200" y="560" width="1520" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="600" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">💡 提供方案时循序渐进：</text>
  <text x="220" y="630" class="speech-text">"我们先试试调整位置/优化信道…如果不行，</text>
  <text x="220" y="660" class="speech-text">可以考虑加个WiFi扩展器/升级Mesh…"</text>
  
  <!-- 推荐增值业务话术 -->
  <rect x="200" y="700" width="1520" height="120" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">🎯 推荐增值业务时自然：</text>
  <text x="220" y="770" class="speech-text">"像您家这种大户型，用Mesh路由器效果是最好的，</text>
  <text x="220" y="800" class="speech-text">全屋信号都很强，而且我们现在有优惠活动…"</text>
  
  <!-- 话术要点 -->
  <rect x="100" y="850" width="1720" height="150" fill="#FEF7ED" rx="25" stroke="#EA580C" stroke-width="3"/>
  <text x="150" y="900" class="section-title" fill="#EA580C">话术要点：</text>
  <text x="200" y="940" class="key-text">✓ 用通俗语言解释技术问题</text>
  <text x="600" y="940" class="key-text">✓ 先免费方案，再付费方案</text>
  <text x="200" y="970" class="key-text">✓ 结合用户实际需求推荐</text>
  <text x="600" y="970" class="key-text">✓ 强调效果和价值</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="1020" width="1520" height="50" fill="#F8FAFC" rx="15"/>
  <text x="960" y="1055" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">专业诊断 + 贴心方案 = 用户信任 + 业务机会</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="500" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="500" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="925" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="925" r="25" fill="#EA580C" opacity="0.3"/>
</svg>
