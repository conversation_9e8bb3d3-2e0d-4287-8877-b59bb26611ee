<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
      .mistake-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #DC2626; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">测速，不只是完成任务</text>
  <text x="960" y="140" class="subtitle-text">"一次测速"：用数据赢得信任</text>
  
  <!-- 为何重要 -->
  <rect x="100" y="180" width="1720" height="150" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#3B82F6">为何重要？</text>
  <text x="150" y="280" class="highlight-text" fill="#059669">用户最关心网速！当面测达标，是建立信任的关键一步。</text>
  <text x="150" y="320" class="content-text">数据不会说谎，眼见为实最有说服力！</text>
  
  <!-- 标准动作 -->
  <rect x="100" y="360" width="1720" height="450" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="410" class="section-title" fill="#059669">标准动作：</text>
  
  <!-- 选点 -->
  <rect x="200" y="450" width="350" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="375" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">1️⃣ 选点</text>
  <text x="220" y="530" class="step-text">客厅电视旁、主卧床头</text>
  <text x="220" y="560" class="step-text">等用户常用区域</text>
  <text x="375" y="590" class="highlight-text" fill="#DC2626" text-anchor="middle">必测！</text>
  
  <!-- 工具 -->
  <rect x="600" y="450" width="350" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="775" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">2️⃣ 工具</text>
  <text x="620" y="530" class="step-text">统一使用爱家APP</text>
  <text x="620" y="560" class="step-text">"一键测速"功能</text>
  <text x="775" y="590" class="highlight-text" fill="#3B82F6" text-anchor="middle">标准化！</text>
  
  <!-- 演示 -->
  <rect x="1000" y="450" width="350" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1175" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">3️⃣ 演示</text>
  <text x="1020" y="530" class="step-text">清晰向用户展示</text>
  <text x="1020" y="560" class="step-text">测速结果</text>
  <text x="1175" y="590" class="highlight-text" fill="#7C3AED" text-anchor="middle">透明化！</text>
  
  <!-- 记录 -->
  <rect x="1400" y="450" width="350" height="150" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="1575" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">4️⃣ 记录</text>
  <text x="1420" y="530" class="step-text">截图上传系统</text>
  <text x="1420" y="560" class="step-text">嵌入电子验收单</text>
  <text x="1575" y="590" class="highlight-text" fill="#EA580C" text-anchor="middle">留痕迹！</text>
  
  <!-- 流程箭头 -->
  <path d="M 550 525 L 600 525" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  <path d="M 950 525 L 1000 525" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  <path d="M 1350 525 L 1400 525" fill="none" stroke="#059669" stroke-width="4" marker-end="url(#arrowhead)"/>
  
  <!-- 测速演示区域 -->
  <rect x="200" y="630" width="1520" height="120" fill="#E0F2FE" rx="15"/>
  <text x="220" y="670" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #0284C7;">📱 演示话术：</text>
  <text x="220" y="710" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic;">"X先生，我现在用爱家APP给您测一下网速，您看，客厅这里是285M，</text>
  <text x="220" y="740" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic;">主卧那边是278M，都超过了您300M套餐的90%标准，网速完全达标！"</text>
  
  <!-- 常见误区 -->
  <rect x="100" y="840" width="1720" height="150" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <text x="150" y="890" class="section-title" fill="#DC2626">常见误区：</text>
  <text x="200" y="930" class="mistake-text">❌ 只在光猫旁测</text>
  <text x="500" y="930" class="mistake-text">❌ 用第三方软件测</text>
  <text x="850" y="930" class="mistake-text">❌ 测完不给用户看</text>
  <text x="200" y="970" class="mistake-text">结果：用户不信任，容易产生纠纷！</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="1010" width="1520" height="60" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1050" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">用数据说话，让用户放心！</text>
  
  <!-- 箭头标记定义 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#059669"/>
    </marker>
  </defs>
</svg>
