<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .rule-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #3B82F6; }
      .rule-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; }
      .interaction-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; }
      .icon-circle { fill: #3B82F6; }
      .accent-curve { fill: none; stroke: #3B82F6; stroke-width: 6; opacity: 0.3; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 装饰弧线 -->
  <path d="M 0 150 Q 480 100 960 150 T 1920 150" class="accent-curve"/>
  <path d="M 0 950 Q 480 1000 960 950 T 1920 950" class="accent-curve"/>
  
  <!-- 标题 -->
  <text x="960" y="120" class="title-text">课堂公约，让我们共同遵守</text>
  
  <!-- 规则1：手机调静音 -->
  <circle cx="150" cy="280" r="40" class="icon-circle"/>
  <text x="150" y="295" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">📱</text>
  <text x="220" y="260" class="rule-title">手机调静音：</text>
  <text x="220" y="300" class="rule-desc">专注学习，尊重你我。</text>
  
  <!-- 规则2：积极参与 -->
  <circle cx="150" cy="400" r="40" class="icon-circle"/>
  <text x="150" y="415" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">🙋</text>
  <text x="220" y="380" class="rule-title">积极参与：</text>
  <text x="220" y="420" class="rule-desc">大胆提问，分享你的经验和困惑。</text>
  
  <!-- 规则3：演练投入 -->
  <circle cx="150" cy="520" r="40" class="icon-circle"/>
  <text x="150" y="535" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">🎭</text>
  <text x="220" y="500" class="rule-title">演练投入：</text>
  <text x="220" y="540" class="rule-desc">把课堂当实战，模拟场景多练习。</text>
  
  <!-- 规则4：小组协作 -->
  <circle cx="150" cy="640" r="40" class="icon-circle"/>
  <text x="150" y="655" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">🤝</text>
  <text x="220" y="620" class="rule-title">小组协作：</text>
  <text x="220" y="660" class="rule-desc">互相帮助，碰撞思维火花。</text>
  
  <!-- 规则5：准时归位 -->
  <circle cx="150" cy="760" r="40" class="icon-circle"/>
  <text x="150" y="775" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: white; text-anchor: middle;">⏰</text>
  <text x="220" y="740" class="rule-title">准时归位：</text>
  <text x="220" y="780" class="rule-desc">合理安排休息，保持学习状态。</text>
  
  <!-- 互动区域 -->
  <rect x="1000" y="250" width="800" height="500" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="1400" y="320" class="interaction-text" text-anchor="middle">互动环节</text>
  
  <text x="1050" y="380" class="rule-desc">📋 讲师简短自我介绍</text>
  
  <text x="1050" y="440" class="rule-desc">👥 学员自我介绍：</text>
  <text x="1080" y="480" class="rule-desc">• 姓名</text>
  <text x="1080" y="520" class="rule-desc">• 岗位</text>
  <text x="1080" y="560" class="rule-desc">• 对本次培训的期待</text>
  
  <text x="1050" y="620" class="rule-desc">🎯 让我们相互了解，</text>
  <text x="1080" y="660" class="rule-desc">共同开启学习之旅！</text>
  
  <!-- 装饰元素 -->
  <circle cx="800" cy="300" r="60" fill="#E0F2FE" opacity="0.6"/>
  <circle cx="800" cy="500" r="80" fill="#DBEAFE" opacity="0.4"/>
  <circle cx="800" cy="700" r="60" fill="#F0F9FF" opacity="0.6"/>
</svg>
