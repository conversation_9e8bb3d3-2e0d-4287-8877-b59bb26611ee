<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .timing-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .method-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; text-anchor: middle; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">润物细无声：在服务中传递品牌价值</text>
  <text x="960" y="140" class="subtitle-text">品牌传递触点1：服务中的"植入"</text>
  
  <!-- 时机 -->
  <rect x="100" y="180" width="1720" height="120" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="3"/>
  <text x="150" y="220" class="section-title" fill="#3B82F6">时机：</text>
  <text x="150" y="260" class="timing-text">安装调试、故障处理、业务咨询时</text>
  <text x="150" y="285" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280;">每一次与用户接触都是品牌传播的黄金时机</text>
  
  <!-- 方法 -->
  <rect x="100" y="330" width="1720" height="550" fill="#F8FAFC" rx="25" stroke="#6B7280" stroke-width="4"/>
  <text x="150" y="380" class="section-title" fill="#1E3A8A">方法：</text>
  
  <!-- 结合产品说 -->
  <rect x="200" y="420" width="1520" height="120" fill="#F0FDF4" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="960" y="460" class="method-title" fill="#059669">结合产品说</text>
  <text x="220" y="490" class="speech-text">"我们这个千兆宽带，属于爱家'智连'服务的一部分，</text>
  <text x="220" y="520" class="speech-text">保证您上网体验…"</text>
  
  <!-- 解答疑问时 -->
  <rect x="200" y="560" width="1520" height="120" fill="#EFF6FF" rx="15" stroke="#3B82F6" stroke-width="2"/>
  <text x="960" y="600" class="method-title" fill="#3B82F6">解答疑问时</text>
  <text x="220" y="630" class="speech-text">"爱家平台上有好多功能，像这个'家庭云'就能解决</text>
  <text x="220" y="660" class="speech-text">您手机存照片的问题…"</text>
  
  <!-- 标准话术应用 -->
  <rect x="200" y="700" width="1520" height="120" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="960" y="740" class="method-title" fill="#EA580C">标准话术应用</text>
  <text x="220" y="770" class="speech-text">在开场白、结束语中自然融入"移动爱家"品牌名</text>
  <text x="220" y="800" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C;">示例："感谢您选择移动爱家服务，我们会为您提供最优质的体验"</text>
  
  <!-- 植入原则 -->
  <rect x="200" y="910" width="1520" height="120" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="950" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">植入原则</text>
  <text x="960" y="980" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #1E3A8A; text-anchor: middle;">自然融入，不生硬；价值导向，不推销</text>
  <text x="960" y="1010" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">让用户在不知不觉中加深对爱家品牌的认知</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="240" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="240" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="50" cy="650" r="25" fill="#6B7280" opacity="0.3"/>
  <circle cx="1870" cy="650" r="25" fill="#6B7280" opacity="0.3"/>
</svg>
