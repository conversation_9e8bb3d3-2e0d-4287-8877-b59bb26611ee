<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .method-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .tool-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .core-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">服务中处处有营销机会</text>
  <text x="960" y="140" class="subtitle-text">"三促"小结：主动服务，挖掘潜力</text>
  
  <!-- 方法总结 -->
  <rect x="100" y="180" width="1720" height="500" fill="#F8FAFC" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <text x="960" y="230" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">方法：</text>
  
  <!-- 多观察 -->
  <rect x="200" y="280" width="700" height="150" fill="#EFF6FF" rx="20" stroke="#3B82F6" stroke-width="3"/>
  <text x="550" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #3B82F6; text-anchor: middle;">多观察</text>
  <text x="220" y="360" class="method-text">用户的使用习惯、设备情况、家庭环境</text>
  <text x="220" y="390" class="tool-text">• 现场设备数量和类型</text>
  <text x="220" y="420" class="tool-text">• 用户抱怨和需求表达</text>
  
  <!-- 多询问 -->
  <rect x="950" y="280" width="700" height="150" fill="#F0FDF4" rx="20" stroke="#059669" stroke-width="3"/>
  <text x="1300" y="320" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #059669; text-anchor: middle;">多询问</text>
  <text x="970" y="360" class="method-text">主动了解用户的需求和痛点</text>
  <text x="970" y="390" class="tool-text">• 网络使用感受如何？</text>
  <text x="970" y="420" class="tool-text">• 有什么不方便的地方？</text>
  
  <!-- 巧推荐 -->
  <rect x="200" y="450" width="700" height="150" fill="#FEF7ED" rx="20" stroke="#EA580C" stroke-width="3"/>
  <text x="550" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #EA580C; text-anchor: middle;">巧推荐</text>
  <text x="220" y="530" class="method-text">结合场景，用对方法和话术</text>
  <text x="220" y="560" class="tool-text">• 针对性解决方案</text>
  <text x="220" y="590" class="tool-text">• 自然融入推荐话术</text>
  
  <!-- 常用工具 -->
  <rect x="950" y="450" width="700" height="150" fill="#F3E8FF" rx="20" stroke="#7C3AED" stroke-width="3"/>
  <text x="1300" y="490" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #7C3AED; text-anchor: middle;">常用工具</text>
  <text x="970" y="530" class="method-text">用户标签、爱家APP、后台系统提醒</text>
  <text x="970" y="560" class="tool-text">• 用户画像分析</text>
  <text x="970" y="590" class="tool-text">• 平台智能推荐</text>
  
  <!-- 三促回顾 -->
  <rect x="200" y="710" width="1520" height="150" fill="#FFFBEB" rx="20" stroke="#F59E0B" stroke-width="3"/>
  <text x="960" y="750" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">三促回顾</text>
  
  <!-- 三促内容 -->
  <rect x="250" y="780" width="400" height="60" fill="#FED7AA" rx="10"/>
  <text x="450" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C; text-anchor: middle;">促活跃</text>
  
  <text x="680" y="810" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="720" y="780" width="400" height="60" fill="#FED7AA" rx="10"/>
  <text x="920" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C; text-anchor: middle;">促升档</text>
  
  <text x="1150" y="810" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #6B7280;">+</text>
  
  <rect x="1190" y="780" width="400" height="60" fill="#FED7AA" rx="10"/>
  <text x="1390" y="815" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: bold; fill: #EA580C; text-anchor: middle;">促续约</text>
  
  <!-- 核心理念 -->
  <rect x="200" y="890" width="1520" height="150" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="960" y="930" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669; text-anchor: middle;">核心理念</text>
  <text x="960" y="970" class="core-text" fill="#1E3A8A">服务中发现机会，沟通中创造价值</text>
  <text x="960" y="1020" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #6B7280; text-anchor: middle;">每一次接触都是深化用户关系的机会</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="380" r="30" fill="#3B82F6" opacity="0.3"/>
  <circle cx="1870" cy="380" r="30" fill="#059669" opacity="0.3"/>
  <circle cx="50" cy="550" r="25" fill="#EA580C" opacity="0.3"/>
  <circle cx="1870" cy="550" r="25" fill="#7C3AED" opacity="0.3"/>
</svg>
