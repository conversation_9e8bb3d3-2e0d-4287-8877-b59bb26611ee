<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .redline-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; fill: #DC2626; }
      .redline-desc { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .warning-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #7F1D1D; }
      .number-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: white; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">牢记三条线，操作不出错</text>
  
  <!-- 红线1：资源不确定，不轻易受理 -->
  <rect x="100" y="150" width="1720" height="250" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="4"/>
  <circle cx="200" cy="220" r="50" fill="#DC2626"/>
  <text x="200" y="240" class="number-text">1</text>
  <text x="280" y="190" class="redline-title">[红线1] 资源不确定，不轻易受理</text>
  <text x="280" y="240" class="redline-desc">地址覆盖不了？端口满了？先核实清楚，或请用户签署预覆盖协议，</text>
  <text x="280" y="280" class="redline-desc">不能为了开单而"盲开"。</text>
  <text x="280" y="330" class="warning-text">⚠️ 盲目受理 → 装不了 → 用户抱怨 → 公司损失 → 个人背锅</text>
  
  <!-- 红线2：指标不达标，不强行交工 -->
  <rect x="100" y="430" width="1720" height="250" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="4"/>
  <circle cx="200" cy="500" r="50" fill="#DC2626"/>
  <text x="200" y="520" class="number-text">2</text>
  <text x="280" y="470" class="redline-title">[红线2] 指标不达标，不强行交工</text>
  <text x="280" y="520" class="redline-desc">光衰太大？速率不够？必须现场调优解决，</text>
  <text x="280" y="560" class="redline-desc">不能把问题留给用户。</text>
  <text x="280" y="610" class="warning-text">⚠️ 强行交工 → 用户投诉 → 返工维修 → 口碑受损 → 恶性循环</text>
  
  <!-- 红线3：验收不完成，不算真搞定 -->
  <rect x="100" y="710" width="1720" height="250" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="4"/>
  <circle cx="200" cy="780" r="50" fill="#DC2626"/>
  <text x="200" y="800" class="number-text">3</text>
  <text x="280" y="750" class="redline-title">[红线3] 验收不完成，不算真搞定</text>
  <text x="280" y="800" class="redline-desc">测速、演示、电子签名，一步都不能少，</text>
  <text x="280" y="840" class="redline-desc">确保用户认可，数据闭环。</text>
  <text x="280" y="890" class="warning-text">⚠️ 草率收尾 → 用户不认账 → 纠纷扯皮 → 工作被动 → 信誉受损</text>
  
  <!-- 底部强调 -->
  <rect x="300" y="990" width="1320" height="70" fill="#7F1D1D" rx="15"/>
  <text x="960" y="1035" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: white; text-anchor: middle;">三条红线不能碰，规范操作保平安！</text>
  
  <!-- 装饰元素 -->
  <rect x="50" y="200" width="10" height="200" fill="#DC2626" rx="5"/>
  <rect x="1860" y="200" width="10" height="200" fill="#DC2626" rx="5"/>
  <rect x="50" y="480" width="10" height="200" fill="#DC2626" rx="5"/>
  <rect x="1860" y="480" width="10" height="200" fill="#DC2626" rx="5"/>
  <rect x="50" y="760" width="10" height="200" fill="#DC2626" rx="5"/>
  <rect x="1860" y="760" width="10" height="200" fill="#DC2626" rx="5"/>
</svg>
