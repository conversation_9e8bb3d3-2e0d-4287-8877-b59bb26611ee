<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .subtitle-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; fill: #3B82F6; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 42px; font-weight: bold; }
      .content-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .signal-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #4B5563; }
      .speech-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 30px; font-weight: normal; fill: #374151; font-style: italic; }
      .highlight-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 34px; font-weight: bold; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">把握时机，推荐Mesh组网方案</text>
  <text x="960" y="140" class="subtitle-text">随销机会：何时推荐Mesh？</text>
  
  <!-- 识别信号 -->
  <rect x="100" y="180" width="1720" height="450" fill="#FEF2F2" rx="25" stroke="#DC2626" stroke-width="3"/>
  <text x="150" y="230" class="section-title" fill="#DC2626">识别信号 (用户抱怨或现场发现)：</text>
  
  <!-- 信号1：用户抱怨 -->
  <rect x="200" y="270" width="700" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="550" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">用户抱怨</text>
  <text x="220" y="340" class="speech-text">"师傅，我这卧室/书房信号太差了！"</text>
  <text x="220" y="370" class="signal-text">直接表达对信号覆盖的不满</text>
  
  <!-- 信号2：现场发现 -->
  <rect x="950" y="270" width="700" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1300" y="310" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">现场发现</text>
  <text x="970" y="340" class="signal-text">现场测速发现某些区域信号弱、速率低</text>
  <text x="970" y="370" class="signal-text">专业检测发现问题</text>
  
  <!-- 信号3：房型特点 -->
  <rect x="200" y="410" width="700" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="550" y="450" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">房型特点</text>
  <text x="220" y="480" class="signal-text">用户家是新装修的大房子/复式/别墅</text>
  <text x="220" y="510" class="signal-text">户型复杂，覆盖难度大</text>
  
  <!-- 信号4：设备多 -->
  <rect x="950" y="410" width="700" height="120" fill="#FEE2E2" rx="15" stroke="#DC2626" stroke-width="2"/>
  <text x="1300" y="450" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #DC2626; text-anchor: middle;">设备多</text>
  <text x="970" y="480" class="signal-text">用户家智能设备多</text>
  <text x="970" y="510" class="signal-text">(手机、平板、电视、摄像头…)</text>
  
  <!-- 推荐话术 -->
  <rect x="100" y="660" width="1720" height="250" fill="#F0FDF4" rx="25" stroke="#059669" stroke-width="3"/>
  <text x="150" y="710" class="section-title" fill="#059669">推荐话术：</text>
  
  <!-- 基础话术 -->
  <rect x="200" y="750" width="1520" height="80" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="780" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">💬 基础版：</text>
  <text x="220" y="810" class="speech-text">"您家这个情况，用一套Mesh路由器就能完美解决了，</text>
  <text x="220" y="830" class="speech-text">保证全屋信号都杠杠的，走到哪网速都快。我们现在有优惠活动…"</text>
  
  <!-- 专业话术 -->
  <rect x="200" y="850" width="1520" height="80" fill="#DCFCE7" rx="15" stroke="#059669" stroke-width="2"/>
  <text x="220" y="880" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #059669;">💬 专业版：</text>
  <text x="220" y="910" class="speech-text">结合爱家APP的户型工具或信号检测结果进行推荐，更有说服力</text>
  
  <!-- 底部强调 -->
  <rect x="200" y="940" width="1520" height="100" fill="#FFFBEB" rx="20"/>
  <text x="960" y="970" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: bold; fill: #F59E0B; text-anchor: middle;">🎯 关键</text>
  <text x="960" y="1000" class="highlight-text" fill="#059669" text-anchor="middle">用数据说话，用专业工具支撑推荐理由</text>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 28px; font-weight: normal; fill: #6B7280; text-anchor: middle;">不是推销，是解决用户实际问题</text>
  
  <!-- 装饰元素 -->
  <circle cx="50" cy="350" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="1870" cy="350" r="30" fill="#DC2626" opacity="0.3"/>
  <circle cx="50" cy="780" r="25" fill="#059669" opacity="0.3"/>
  <circle cx="1870" cy="780" r="25" fill="#059669" opacity="0.3"/>
  
  <!-- 信号图标装饰 -->
  <text x="50" y="500" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #DC2626;">📶</text>
  <text x="1870" y="500" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; fill: #DC2626;">📶</text>
</svg>
