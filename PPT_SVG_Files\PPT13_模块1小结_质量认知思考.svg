<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 64px; font-weight: bold; fill: #1E3A8A; text-anchor: middle; }
      .section-title { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; font-weight: bold; }
      .review-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #374151; }
      .think-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 36px; font-weight: bold; fill: #DC2626; }
      .question-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 32px; font-weight: normal; fill: #4B5563; }
      .icon-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 48px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="white"/>
  
  <!-- 标题 -->
  <text x="960" y="80" class="title-text">本章小结 &amp; 思考</text>
  
  <!-- 回顾区域 -->
  <rect x="100" y="150" width="1720" height="350" fill="#EFF6FF" rx="25" stroke="#3B82F6" stroke-width="4"/>
  <circle cx="200" cy="220" r="40" fill="#3B82F6"/>
  <text x="200" y="235" class="icon-text" fill="white">📋</text>
  <text x="280" y="200" class="section-title" fill="#3B82F6">回顾：</text>
  
  <text x="280" y="260" class="review-text">✓ 用户需求在升级：从"能用"到"好用"再到"爱用"</text>
  <text x="280" y="310" class="review-text">✓ 服务质量是关键：每个触点都影响用户感知</text>
  <text x="280" y="360" class="review-text">✓ NPS是衡量标准：推荐者越多，口碑越好</text>
  <text x="280" y="410" class="review-text">✓ 做好服务多方共赢：用户满意、公司发展、个人成长</text>
  <text x="280" y="460" class="review-text">✓ 关键指标要关注：装机成功率、测速达标率、万投比、NPS</text>
  
  <!-- 思考区域 -->
  <rect x="100" y="550" width="1720" height="400" fill="#FFFBEB" rx="25" stroke="#F59E0B" stroke-width="4"/>
  <circle cx="200" cy="620" r="40" fill="#F59E0B"/>
  <text x="200" y="635" class="icon-text" fill="white">🤔</text>
  <text x="280" y="600" class="section-title" fill="#F59E0B">思考：</text>
  
  <text x="280" y="680" class="think-text">在我的日常工作中，哪些环节可以做得更好，</text>
  <text x="280" y="730" class="think-text">来提升用户满意度？</text>
  
  <!-- 思考提示 -->
  <rect x="320" y="780" width="1280" height="140" fill="#FEF7ED" rx="15" stroke="#EA580C" stroke-width="2"/>
  <text x="360" y="820" class="question-text">💡 思考提示：</text>
  <text x="360" y="860" class="question-text">• 我在安装过程中，哪个步骤最容易出问题？</text>
  <text x="360" y="895" class="question-text">• 用户最常抱怨什么？我能如何改进？</text>
  
  <!-- 底部强调 -->
  <rect x="300" y="980" width="1320" height="80" fill="#F0FDF4" rx="15"/>
  <text x="960" y="1030" style="font-family: 'Microsoft YaHei', sans-serif; font-size: 40px; font-weight: bold; fill: #059669; text-anchor: middle;">认知决定行动，行动创造价值！</text>
  
  <!-- 装饰元素 -->
  <circle cx="1650" cy="300" r="60" fill="#E0F2FE" opacity="0.6"/>
  <circle cx="1750" cy="400" r="40" fill="#DBEAFE" opacity="0.5"/>
  <circle cx="1650" cy="700" r="50" fill="#FEF3C7" opacity="0.6"/>
  <circle cx="1750" cy="800" r="35" fill="#FDE68A" opacity="0.5"/>
</svg>
